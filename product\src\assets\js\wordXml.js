class WordXml {
  constructor() {
    this.imgData = []
    this.colorObj = {
      white: '#FFFFFF',
      ivory: '#FFFFF0',
      lightyellow: '#FFFFE0',
      yellow: '#FFFF00',
      snow: '#FFFAFA',
      floralwhite: '#FFFAF0',
      lemonchiffon: '#FFFACD',
      cornsilk: '#FFF8DC',
      seashell: '#FFF5EE',
      lavenderblush: '#FFF0F5',
      papayawhip: '#FFEFD5',
      blanche<PERSON>mond: '#FFEBCD',
      mistyrose: '#FFE4E1',
      bisque: '#FFE4C4',
      moccasin: '#FFE4B5',
      navajowhite: '#FFDEAD',
      peachpuff: '#FFDAB9',
      gold: '#FFD700',
      pink: '#FFC0CB',
      lightpink: '#FFB6C1',
      orange: '#FFA500',
      lightsalmon: '#FFA07A',
      darkorange: '#FF8C00',
      coral: '#FF7F50',
      hotpink: '#FF69B4',
      tomato: '#FF6347',
      orangered: '#FF4500',
      deeppink: '#FF1493',
      fuchsia: '#FF00FF',
      magenta: '#FF00FF',
      red: '#FF0000',
      oldlace: '#FDF5E6',
      lightgoldenrodyellow: '#FAFAD2',
      linen: '#FAF0E6',
      antiquewhite: '#FAEBD7',
      salmon: '#FA8072',
      ghostwhite: '#F8F8FF',
      mintcream: '#F5FFFA',
      whitesmoke: '#F5F5F5',
      beige: '#F5F5DC',
      wheat: '#F5DEB3',
      sandybrown: '#F4A460',
      azure: '#F0FFFF',
      honeydew: '#F0FFF0',
      aliceblue: '#F0F8FF',
      khaki: '#F0E68C',
      lightcoral: '#F08080',
      palegoldenrod: '#EEE8AA',
      violet: '#EE82EE',
      darksalmon: '#E9967A',
      lavender: '#E6E6FA',
      lightcyan: '#E0FFFF',
      burlywood: '#DEB887',
      plum: '#DDA0DD',
      gainsboro: '#DCDCDC',
      crimson: '#DC143C',
      palevioletred: '#DB7093',
      goldenrod: '#DAA520',
      orchid: '#DA70D6',
      thistle: '#D8BFD8',
      lightgray: '#D3D3D3',
      lightgrey: '#D3D3D3',
      tan: '#D2B48C',
      chocolate: '#D2691E',
      peru: '#CD853F',
      indianred: '#CD5C5C',
      mediumvioletred: '#C71585',
      silver: '#C0C0C0',
      darkkhaki: '#BDB76B',
      rosybrown: '#BC8F8F',
      mediumorchid: '#BA55D3',
      darkgoldenrod: '#B8860B',
      firebrick: '#B22222',
      powderblue: '#B0E0E6',
      lightsteelblue: '#B0C4DE',
      paleturquoise: '#AFEEEE',
      greenyellow: '#ADFF2F',
      lightblue: '#ADD8E6',
      darkgray: '#A9A9A9',
      darkgrey: '#A9A9A9',
      brown: '#A52A2A',
      sienna: '#A0522D',
      darkorchid: '#9932CC',
      palegreen: '#98FB98',
      darkviolet: '#9400D3',
      mediumpurple: '#9370DB',
      lightgreen: '#90EE90',
      darkseagreen: '#8FBC8F',
      saddlebrown: '#8B4513',
      darkmagenta: '#8B008B',
      darkred: '#8B0000',
      blueviolet: '#8A2BE2',
      lightskyblue: '#87CEFA',
      skyblue: '#87CEEB',
      gray: '#808080',
      grey: '#808080',
      olive: '#808000',
      purple: '#800080',
      maroon: '#800000',
      aquamarine: '#7FFFD4',
      chartreuse: '#7FFF00',
      lawngreen: '#7CFC00',
      mediumslateblue: '#7B68EE',
      lightslategray: '#778899',
      lightslategrey: '#778899',
      slategray: '#708090',
      slategrey: '#708090',
      olivedrab: '#6B8E23',
      slateblue: '#6A5ACD',
      dimgray: '#696969',
      dimgrey: '#696969',
      mediumaquamarine: '#66CDAA',
      cornflowerblue: '#6495ED',
      cadetblue: '#5F9EA0',
      darkolivegreen: '#556B2F',
      indigo: '#4B0082',
      mediumturquoise: '#48D1CC',
      darkslateblue: '#483D8B',
      steelblue: '#4682B4',
      royalblue: '#4169E1',
      turquoise: '#40E0D0',
      mediumseagreen: '#3CB371',
      limegreen: '#32CD32',
      darkslategray: '#2F4F4F',
      darkslategrey: '#2F4F4F',
      seagreen: '#2E8B57',
      forestgreen: '#228B22',
      lightseagreen: '#20B2AA',
      dodgerblue: '#1E90FF',
      midnightblue: '#191970',
      aqua: '#00FFFF',
      cyan: '#00FFFF',
      springgreen: '#00FF7F',
      lime: '#00FF00',
      mediumspringgreen: '#00FA9A',
      darkturquoise: '#00CED1',
      deepskyblue: '#00BFFF',
      darkcyan: '#008B8B',
      teal: '#008080',
      green: '#008000',
      darkgreen: '#006400',
      blue: '#0000FF',
      mediumblue: '#0000CD',
      darkblue: '#00008B',
      navy: '#000080',
      black: '#000000'
    }
  }

  initData (html) {
    this.imgData = []
    return new Promise((resolve, reject) => {
      var obj = document.createElement('div')
      obj.innerHTML = html
      var img = obj.querySelectorAll('img')
      this.getBase64Image(img).then(res => {
        var xml = this.xmlData(obj.children)
        var data = []
        this.imgData.forEach(item => {
          res.forEach(row => {
            if (item.url === row.url) {
              data.push({ id: item.id, name: item.name, file: row.file })
              var width = '9525width' + item.id
              var height = '9525height' + item.id
              xml = xml.replace(new RegExp(width, 'g'), (9525 * row.width) + '')
              xml = xml.replace(new RegExp(height, 'g'), (9525 * row.height) + '')
            }
          })
        })
        resolve({ imgData: data, xml: xml })
      })
    })
  }

  getBase64Image (img) {
    return new Promise((resolve, reject) => {
      if (img.length) {
        var imgData = []
        img.forEach(item => {
          const image = new Image()
          image.src = item.src // 处理缓存
          image.crossOrigin = '*' // 支持跨域图片
          image.onload = () => {
            var canvas = document.createElement('canvas')
            canvas.width = image.width
            canvas.height = image.height
            var ctx = canvas.getContext('2d')
            ctx.drawImage(image, 0, 0, image.width, image.height)
            var dataURL = canvas.toDataURL('image/png') // 可选其他值 image/jpeg
            imgData.push({ url: item.src, width: image.width, height: image.height, file: dataURL.replace(/^data:image\/(png|jpg|jpeg);base64,/, '') })
            if (imgData.length === img.length) {
              resolve(imgData)
            }
          }
        })
      } else {
        resolve(imgData)
      }
    })
  }

  xmlData (data) {
    var xml = ''
    data.forEach(item => {
      if (item.tagName.toLowerCase() === 'p' || item.tagName.toLowerCase() === 'div') {
        xml += '<w:p>'
        xml += this.xmlStyle(item.style, true, this.firstLineIndentation(item))
        if (item.children.length) {
          var fontSize = item.style.fontSize ? item.style.fontSize : '16px'
          xml += this.spanXml(item.childNodes, this.xmlStyle(item.style, false), false, fontSize)
        } else {
          var spanStyle = this.xmlStyle(item.style, false)
          xml += `<w:r>${spanStyle}<w:t>${item.innerText}</w:t> </w:r>`
        }
        xml += '</w:p>'
      } else if (item.tagName.toLowerCase() === 'table') {
        xml += '<w:tbl>'
        xml += '<w:tblPr>'
        xml += '<w:tblBorders>'
        xml += '<w:top w:val="single" w:sz="4" w:space="0" w:color="auto"/>'
        xml += '<w:left w:val="single" w:sz="4" w:space="0" w:color="auto"/>'
        xml += '<w:bottom w:val="single" w:sz="4" w:space="0" w:color="auto"/>'
        xml += '<w:right w:val="single" w:sz="4" w:space="0" w:color="auto"/>'
        xml += '<w:insideH w:val="single" w:sz="4" w:space="0" w:color="auto"/>'
        xml += '<w:insideV w:val="single" w:sz="4" w:space="0" w:color="auto"/>'
        xml += '</w:tblBorders><w:tblW w:w="0" w:type="auto"/>'
        xml += '<w:tblLayout w:type="autofit"/><w:tblCellMar>'
        xml += '<w:top w:w="220" w:type="dxa"/>'
        xml += '<w:start w:w="220" w:type="dxa"/>'
        xml += '<w:bottom w:w="220" w:type="dxa"/>'
        xml += '<w:end w:w="220" w:type="dxa"/>'
        xml += '</w:tblCellMar>'
        xml += '</w:tblPr>'
        if (item.children[0].children.length) {
          xml += this.tableXml(item.children[0].children)
        }
        xml += '</w:tbl>'
      } else if (item.tagName.toLowerCase() === 'article') {
        xml = this.xmlData(item.children)
      }
    })
    console.log('xml==>>>', xml)
    return xml
  }

  // 表格处理
  tableXml (data) {
    var xml = ''
    var rowspan = []
    var rowspanI = []
    var rowspanWidth = []
    var isIndex = 0
    data.forEach(item => {
      xml += '<w:tr>'
      rowspanI.forEach((item, index) => {
        if (item === 0 && rowspan[index] !== 0) {
          isIndex = isIndex + 1
          xml += `<w:tc><w:tcPr>${rowspanWidth[index] ? `<w:tcW w:w="${rowspanWidth[index]}" w:type="dxa"/>` : ''}<w:gridSpan w:val="2"/><w:vMerge/></w:tcPr><w:p/></w:tc>`
          rowspan[index] = rowspan[index] - 1
        }
      })
      item.children.forEach((row, i) => {
        xml += '<w:tc><w:tcPr>'
        if (row.rowSpan !== 1) {
          rowspan.push(row.rowSpan - 1)
          rowspanI.push(i)
          rowspanWidth.push(row.width || 0)
          xml += '<w:vMerge w:val="restart"/><w:vAlign w:val="center"/>'
        }
        if (row.colSpan !== 1) {
          var width = this.colSpanWidth(data, i, row.colSpan - 1)
          xml += `<w:tcW w:type="pct" w:w="${width}"/>`
        }
        if (row.width) {
          xml += `<w:tcW w:type="pct" w:w="${row.width}"/>`
        }
        xml += '</w:tcPr>'
        if (row.children.length) {
          var html = '<w:p>'
          row.childNodes.forEach(element => {
            if (element.nodeName.toLowerCase() === 'p') {
              xml += '<w:p>'
              xml += this.xmlStyle(element.style, true, this.firstLineIndentation(row))
              if (element.children.length) {
                const fontSize = element.style.fontSize ? item.style.fontSize : '16px'
                xml += this.spanXml(element.childNodes, this.xmlStyle(element.style, false), false, fontSize)
              } else {
                xml += `<w:r>${this.xmlStyle(element.style, false)}<w:t>${element.innerText}</w:t> </w:r>`
              }
              xml += '</w:p>'
            } else {
              const fontSize = element.style.fontSize ? item.style.fontSize : '16px'
              html += this.spanXml([element], this.xmlStyle(element.style, false), false, fontSize)
            }
          })
          html += '</w:p>'
          if (html.length > 11) {
            xml += html
          }
        } else {
          xml += '<w:p>'
          xml += `<w:r>${this.xmlStyle(row.style, false)}<w:t>${row.innerText}</w:t> </w:r>`
          xml += '</w:p>'
        }
        xml += '</w:tc>'
        rowspanI.forEach((item, index) => {
          if (item && item - isIndex === i + 1 && rowspan[index] !== 0) {
            isIndex = isIndex + 1
            xml += `<w:tc><w:tcPr>${rowspanWidth[index] ? `<w:tcW w:w="${rowspanWidth[index]}" w:type="dxa"/>` : ''}<w:gridSpan w:val="2"/><w:vMerge/></w:tcPr><w:p/></w:tc>`
            rowspan[index] = rowspan[index] - 1
          }
        })
      })
      isIndex = 0
      xml += '</w:tr>'
    })
    return xml
  }

  // 横向合并单元格
  colSpanWidth (data, index, length) {
    var arr = []
    var width = 0
    data.forEach((item, i) => {
      if (item.children.length > arr.length) {
        arr = item.children
        var iswidth = 0
        item.children.forEach((row, ins) => {
          if (ins >= index && ins <= index + length) {
            if (row.width) {
              iswidth += Number(row.width)
            }
          }
        })
        width = iswidth
      }
    })
    return width
  }

  spanXml (data, style, type, fontSize, isNesting = false) {
    var xml = ''
    data.forEach(item => {
      if (item.nodeName === '#text') {
        xml += `<w:r>${style}<w:t>${item.nodeValue}</w:t> </w:r>`
      } else if (item.nodeName.toLowerCase() === 'strong') {
        if (item.children.length) {
          const fontSizeNum = item.style.fontSize ? item.style.fontSize : fontSize
          xml += this.spanXml(item.childNodes, this.xmlStyle({ strong: '<w:b />' }, false, 0, fontSize), true, fontSizeNum, true)
        } else {
          xml += `<w:r>${style}<w:rPr><w:b /></w:rPr><w:t>${item.innerText}</w:t> </w:r>`
        }
      } else if (item.nodeName.toLowerCase() === 'em') {
        if (item.children.length) {
          const fontSizeNum = item.style.fontSize ? item.style.fontSize : fontSize
          xml += this.spanXml(item.childNodes, this.xmlStyle({ em: '<w:i />' }, false, 0, fontSize), false, fontSizeNum, true)
        } else {
          xml += `<w:r>${style}<w:rPr><w:i /></w:rPr><w:t>${item.innerText}</w:t> </w:r>`
        }
      } else if (item.nodeName.toLowerCase() === 'img') {
        xml += `<w:r>${style}<w:rPr></w:rPr><w:r><w:drawing><wp:inline distT="0" distB="0" distL="0" distR="0"><wp:extent cx="9525width1000${this.imgData.length}" cy="9525height1000${this.imgData.length}"/><wp:effectExtent l="0" t="0" r="7620" b="7620"/><wp:docPr id="1000${this.imgData.length}" name="图片 1000${this.imgData.length}"/><wp:cNvGraphicFramePr><a:graphicFrameLocks xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" noChangeAspect="1"/></wp:cNvGraphicFramePr><a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main"><a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture"><pic:pic xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture"><pic:nvPicPr><pic:cNvPr id="1000${this.imgData.length}" name="图片 1000${this.imgData.length}"/><pic:cNvPicPr><a:picLocks noChangeAspect="1"/></pic:cNvPicPr></pic:nvPicPr><pic:blipFill><a:blip r:embed="rId1000${this.imgData.length}"/><a:stretch><a:fillRect/></a:stretch></pic:blipFill><pic:spPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="9525width1000${this.imgData.length}" cy="9525height1000${this.imgData.length}"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom><a:noFill/><a:ln><a:noFill/></a:ln></pic:spPr></pic:pic></a:graphicData></a:graphic></wp:inline></w:drawing></w:r></w:r>`
        this.imgData.push({ id: '1000' + this.imgData.length, name: `images1000${this.imgData.length}.png`, url: item.src })
      } else {
        const fontSizeNum = item.style.fontSize ? item.style.fontSize : fontSize
        var spanStyle = this.xmlStyle(item.style, false, 0, fontSize)
        xml += this.spanXml(item.childNodes, (type || isNesting) ? style + spanStyle : spanStyle, false, fontSizeNum)
        // console.log(xml, '-111----')
        console.log('=======>>>>>>', style, '==-----===', spanStyle, false, fontSizeNum)
      }
    })
    return xml
  }

  // 计算首行缩进
  firstLineIndentation (obj, type) {
    var num = 0
    if (obj.style.fontSize) {
      num = this.sizeCalculate(obj.style.fontSize)
    } else {
      num = 24
    }
    if (obj.children.length) {
      if (obj.childNodes[0].nodeName !== '#text') {
        var numText = this.firstLineIndentation(obj.children[0], true)
        num = numText === 24 ? num : numText
      }
    }
    const sizeNum = Math.floor(((num / 2) / 72) * 96)
    return type ? num : sizeNum * 15
  }

  xmlStyle (style = {}, type, num, fontSize) {
    var text = type ? '<w:pPr>' : '<w:rPr>'
    if (type && style.textIndent) {
      text += `<w:ind w:firstLine="${num * 2}"/>`
    }
    if (type && (style.lineHeight || style.marginTop || style.marginBottom)) {
      var lineHeight = ''
      var marginTop = ''
      var marginBottom = ''
      if (style.lineHeight) {
        var lineHeightNum = style.lineHeight.replace(/em/ig, '')
        if (lineHeightNum.indexOf('%') !== -1) {
          lineHeightNum = Number(lineHeightNum.replace(/%/ig, '')) / 100
        } else if (lineHeightNum.indexOf('px') !== -1) {
          if (style.fontSize) {
            if (style.fontSize.indexOf('pt') !== -1) {
              lineHeightNum = style.fontSize.indexOf('pt') / Number(lineHeightNum.replace(/px/ig, ''))
            } else if (style.fontSize.indexOf('px') !== -1) {
              lineHeightNum = style.fontSize.indexOf('px') / Number(lineHeightNum.replace(/px/ig, ''))
            }
          } else {
            lineHeightNum = 16 / Number(lineHeightNum.replace(/px/ig, ''))
          }
        } else if (lineHeightNum.indexOf('pt') !== -1) {
          if (style.fontSize) {
            if (style.fontSize.indexOf('pt') !== -1) {
              lineHeightNum = style.fontSize.indexOf('pt') / Number(lineHeightNum.replace(/pt/ig, ''))
            } else if (style.fontSize.indexOf('px') !== -1) {
              lineHeightNum = style.fontSize.indexOf('px') / Number(lineHeightNum.replace(/pt/ig, ''))
            }
          } else {
            lineHeightNum = 16 / Number(lineHeightNum.replace(/pt/ig, ''))
          }
        }
        lineHeight = `w:line="${Number(lineHeightNum) * 240}"`
      }
      if (style.marginTop) {
        var marginTopNum = Number(style.marginTop.replace(/px/ig, ''))
        marginTop = `w:before="${((marginTopNum / 10) * 120) + 120}"`
      }
      if (style.marginBottom) {
        var marginBottomNum = Number(style.marginBottom.replace(/px/ig, ''))
        marginBottom = `w:after="${((marginBottomNum / 10) * 120) + 120}"`
      }
      text += `<w:spacing ${marginTop} ${marginBottom} ${lineHeight} w:lineRule="auto" w:beforeAutospacing="0" w:afterAutospacing="0"/>`
    }
    if (style.textAlign) {
      text += `<w:jc w:val="${style.textAlign}"/>`
    }
    if (style.fontSize) {
      const sz = this.sizeCalculate(style.fontSize)
      if (type) {
        text += `<w:rPr><w:sz w:val="${sz}"/></w:rPr>`
      } else {
        text += `<w:sz w:val="${sz}"/>`
      }
    } else {
      if (fontSize) {
        const sz = this.sizeCalculate(fontSize)
        if (type) {
          text += `<w:rPr><w:sz w:val="${sz}"/></w:rPr>`
        } else {
          text += `<w:sz w:val="${sz}"/>`
        }
      } else {
        if (type) {
          text += '<w:rPr><w:sz w:val="24"/></w:rPr>'
        } else {
          text += '<w:sz w:val="24"/>'
        }
      }
    }
    if (style.strong) {
      text += style.strong
    }
    if (style.em) {
      text += style.em
    }
    if (style.color) {
      if (/^[A-Fa-f0-9]{1,4}$/.test(style.color)) {
        text += `<w:color w:val="${this.colorRGBtoHex(style.color)}" />`
      } else {
        text += `<w:color w:val="${style.color}" />`
      }
    }
    if ((style.textDecoration && style.textDecoration !== 'none') || (style.textDecorationLine && style.textDecorationLine !== 'none')) {
      console.log('text', text)
      text += '<w:u w:val="single"/>'
    }
    if (style.backgroundColor) {
      if (/^[A-Fa-f0-9]{1,4}$/.test(style.backgroundColor)) {
        text += `<w:shd w:val="clear" w:fill="${style.backgroundColor}" w:color="${style.backgroundColor}" />`
      } else {
        text += `<w:shd w:val="clear" w:fill="${this.colorRGBtoHex(style.backgroundColor)}" w:color="${this.colorRGBtoHex(style.backgroundColor)}" />`
      }
    }
    if (style.fontFamily) {
      text += `<w:rFonts w:ascii="${style.fontFamily}" w:eastAsia="${style.fontFamily}" w:hAnsi="${style.fontFamily}" />`
    }
    text += type ? '</w:pPr>' : '</w:rPr>'
    return text
  }

  // rgb转十六进制方法
  colorRGBtoHex (color) {
    var rgb = color.split(',')
    if (rgb.length !== 3) {
      return this.colorObj[color] || '#ffffff'
    }
    var r = parseInt(rgb[0].split('(')[1])
    var g = parseInt(rgb[1])
    var b = parseInt(rgb[2].split(')')[0])
    var hex = '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
    return hex
  }

  // 把字体大小换算成以磅为单位
  sizeCalculate (size) {
    var num = 0
    if (size.indexOf('pt') !== -1) {
      num = Number(size.replace(/pt/g, '')) * 2
    } else if (size.indexOf('px') !== -1) {
      num = Math.ceil((Number(size.replace(/px/g, '')) / 96) * 72) * 2
    }
    return num
  }
}

export { WordXml }
