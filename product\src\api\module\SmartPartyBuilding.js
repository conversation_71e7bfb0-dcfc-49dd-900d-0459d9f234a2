import { post, get } from '../http'

const SmartPartyBuilding = {

  // 获取上级党组织
  getParentList (params) {
    return post('/partyorganization/getParentList', params)
  },
  // 获取党建列表
  getOrganizationList (params) {
    return post('/partyorganization/list', params)
  },
  // 添加或编辑党组织
  addEditOrganization (url, params) {
    return post(url, params)
  },
  // 获取党组织详情
  getPartyorganizationInfo (params) {
    return get(`/partyorganization/info/${params}`)
  },
  // 获取党建列表
  partyorganizationDel (params) {
    return post('/partyorganization/del/' + params)
  },
  // 添加成员
  partyorganizationuserAddEdit (url, params) {
    return post(url, params)
  },
  // 获取党组织详情
  getPartyOrganizationsUser (params) {
    return get(`/partyorganization/getPartyOrganizationsUser?id=${params}`)
  },
  // 获取所属党支部
  getChildLevelList () {
    return post('/partyorganization/getChildLevelList')
  },
  // 初始化
  initData (params) {
    return post('/partyorganization/initData', params)
  }
}

export default SmartPartyBuilding
