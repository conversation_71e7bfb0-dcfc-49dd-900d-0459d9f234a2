// 导入封装的方法
import { post, filedownload, exportFile } from '../http'

const suggest = {
  // 届次编号
  circleBoutInfoList (params) {
    return post('/submission/suggest/circleBoutInfo/list', params)
  },
  circleBoutInfoInfo (params) {
    return post(`/submission/suggest/circleBoutInfo/info/${params}`)
  },
  // 届次年
  yearRelationList (params) {
    return post('/submission/suggest/circleboutyear/list', params)
  },
  yearRelationInfo (params) {
    return post(`/submission/suggest/circleboutyear/info/${params}`)
  },
  yearRelationDel (params) {
    return post('/submission/suggest/circleboutyear/dels?', params)
  },
  // 提案分类
  suggestTypeList (params) {
    return post('/submission/suggest/topic/list?', params)
  },
  suggestTypeInfo (params) {
    return post(`/submission/suggest/topic/info/${params}`)
  },
  // 提案业务用户关系
  businessUser (params) {
    return post('/submission/suggest/businessUser/list', params)
  },
  businessUserdels (params) {
    return post('/submission/suggest/businessUser/dels', params)
  },
  enumData (params) {
    return post('/submission/suggest/businessUser/enumData', params)
  },
  enumDatainfo (params) {
    return post(params)
  },
  businessUserInfo (params) {
    return post(`/submission/suggest/businessUser/info/${params}`)
  },
  // 获取提案分类和主题词
  chooseList (params) {
    return post('/submission/suggest/topic/chooseList', params)
  },
  // 获取当前届次
  currentCircleAndBout (params) {
    return post('/submission/suggest/circleBoutInfo/currentCircleAndBout', params)
  },
  // 流程配置列表
  flowconfigList (params) {
    return post('/flowconfig/suggest/list', params)
  },
  // 流程配置详情
  flowconfigInfo (params) {
    return post(`/flowconfig/suggest/info/${params}`)
  },
  // 流程配置删除
  flowconfigDel (params) {
    return post(`/flowconfig/suggest/del/${params}`)
  },
  // 流程配置获取
  getConfigMap (params) {
    return post('/flowconfig/suggest/getConfigMap', params)
  },

  // 所有提案列表
  suggestList (params) {
    return post('/suggest/list', params)
  },
  // 提案委删除提案
  powerDelete (params) {
    return post('/suggest/powerDelete', params)
  },
  // 提案基础详情
  suggestInfo (params) {
    return post(`/suggest/info/${params}`)
  },
  // 我领衔的提案
  mySuggestList (params) {
    return post('/suggest/mySuggestList', params)
  },
  // 我联名的提案
  myJoinSuggestList (params) {
    return post('/suggest/myJoinSuggestList', params)
  },
  // 我的草稿提案
  myDraftsSuggestList (params) {
    return post('/suggest/myDraftsSuggestList', params)
  },
  // 委员删除提案
  representDelete (params) {
    return post('/suggest/representDelete', params)
  },
  // 解锁审查提案
  unlockSuggest (params) {
    return post('/suggest/unlockSuggest', params)
  },
  // 获取提案审查详情
  auditDetail (params) {
    return post('/suggest/auditDetail', params)
  },
  // 代表团审查提案
  representAuditList (params) {
    return post('/suggest/representAuditList', params)
  },
  // 待审查提案
  examineList (params) {
    return post('/suggest/examineList', params)
  },
  // 待复审提案
  reviewList (params) {
    return post('/suggest/reviewList', params)
  },
  // 待审定提案
  lastAuditList (params) {
    return post('/suggest/lastAuditList', params)
  },
  // 不予立案
  rejectList (params) {
    return post('/suggest/rejectList', params)
  },
  // 转参阅件
  referList (params) {
    return post('/suggest/referList', params)
  },
  // 撤案
  banList (params) {
    return post('/suggest/banList', params)
  },
  // 交办详情
  assignDetail (params) {
    return post('/suggest/assignDetail', params)
  },
  // 人大交办中
  RDAssignList (params) {
    return post('/suggest/RDAssignList', params)
  },
  // 政府交办中列表
  ZFAssignList (params) {
    return post('/suggest/ZFAssignList', params)
  },
  // 党委交办中列表
  DWAssignList (params) {
    return post('/suggest/DWAssignList', params)
  },
  // 两院交办中列表
  LYAssignList (params) {
    return post('/suggest/LYAssignList', params)
  },
  // 法院交办中列表
  FYAssignList (params) {
    return post('/suggest/FYAssignList', params)
  },
  // 检察院交办中列表
  JCYAssignList (params) {
    return post('/suggest/JCYAssignList', params)
  },
  // 提案批量二次交办
  batchSecondAssign (params) {
    return post('/suggest/batchSecondAssign', params)
  },
  // 提案批量交办办理单位
  batchAssignSuggest (params) {
    return post('/suggest/batchAssignSuggest', params)
  },
  // 提案委待签收
  allPreAssignList (params) {
    return post('/suggest/allPreAssignList', params)
  },
  // 提案委申请调整
  preAssignReviseList (params) {
    return post('/suggest/preAssignReviseList', params)
  },
  // 提案委审查预交办申请调整提案
  auditReviseTransact (params) {
    return post('/suggest/auditReviseTransact', params)
  },
  // 单位待签收列表
  groupPreAssignList (params) {
    return post('/suggest/groupPreAssignList', params)
  },
  // 单位申请调整列表
  groupPreAssignReviseList (params) {
    return post('/suggest/groupPreAssignReviseList', params)
  },
  // 单位历史申请调整列表
  groupPreAssignHistoryList (params) {
    return post('/suggest/groupPreAssignHistoryList', params)
  },
  // 提案委办理中提案
  allTransactList (params) {
    return post('/suggest/allTransactList', params)
  },
  // 办理中提案详情
  transactSuggestDetail (params) {
    return post('/suggest/transactSuggestDetail', params)
  },
  // 办理单位办理中提案
  groupTransactList (params) {
    return post('/suggest/groupTransactList', params)
  },
  // 办理单位更新内部流程
  updateTransactInnerStatus (params) {
    return post('/suggest/updateTransactInnerStatus', params)
  },
  // 办理单位沟通情况列表
  flowContactList (params) {
    return post('/suggest/flowContactList', params)
  },
  // 办理单位新增沟通情况获取沟通联系人
  contactPersonList (params) {
    return post('/suggest/contactPersonList', params)
  },
  // 可添加沟通情况的办理信息
  contactTransactList (params) {
    return post('/suggest/contactTransactList', params)
  },
  // 提案委删除办理单位沟通交流
  superDeleteFlowContact (params) {
    return post('/suggest/superDeleteFlowContact', params)
  },
  // 办理单位办理中提案申请延期
  saveFlowDelay (params) {
    return post('/suggest/saveFlowDelay', params)
  },
  // 办理单位申请调整办理单位
  exchangeTransact (params) {
    return post('/suggest/exchangeTransact', params)
  },
  // 通过办理记录id获取答复件详情
  flowAnswerDetail (params) {
    return post('/suggest/flowAnswerDetail', params)
  },
  // 办理单位保存答复件
  saveFlowAnswer (params) {
    return post('/suggest/saveFlowAnswer', params)
  },
  // 根据答复件id获取答复件详情
  suggestAnswerDetail (params) {
    return post('/suggest/suggestAnswerDetail', params)
  },
  // 提案委已答复列表
  allAnsweredSuggestList (params) {
    return post('/suggest/allAnsweredSuggestList', params)
  },
  // 办理单位已答复列表
  groupAnsweredSuggestList (params) {
    return post('/suggest/groupAnsweredSuggestList', params)
  },
  // 提案重新办理
  reTransact (params) {
    return post('/suggest/reTransact', params)
  },
  // 提案办结
  finishSuggest (params) {
    return post('/suggest/finishSuggest', params)
  },
  // 提案委批量办结提案
  batchFinishSuggest (params) {
    return post('/suggest/batchFinishSuggest', params)
  },
  // 提案委已办结提案列表
  allFinishSuggestList (params) {
    return post('/suggest/allFinishSuggestList', params)
  },
  // 办理单位已办结提案列表
  groupFinishSuggestList (params) {
    return post('/suggest/groupFinishSuggestList', params)
  },
  // 委员确认联名关系
  confirmJoinSubmit (params) {
    return post('/suggest/confirmJoinSubmit', params)
  },
  // 提案办理评价详情
  flowEvaluateDetail (params) {
    return post('/suggest/flowEvaluateDetail', params)
  },
  // 保存提案办理评价
  saveFlowEvaluate (params) {
    return post('/suggest/saveFlowEvaluate', params)
  },
  // 超管提案办理评价列表
  flowEvaluateList (params) {
    return post('/suggest/flowEvaluateList', params)
  },
  // 超管新增提案办理评价
  superAddFlowEvaluate (params) {
    return post('/suggest/superAddFlowEvaluate', params)
  },
  // 超管编辑提案办理评价
  superEditFlowEvaluate (params) {
    return post('/suggest/superEditFlowEvaluate', params)
  },
  // 超管删除提案办理评价
  superDeleteFlowEvaluate (params) {
    return post('/suggest/superDeleteFlowEvaluate', params)
  },
  // 超管同意所有未操作联名数据
  superAgreeJoinSubmit (params) {
    return post('/suggest/superAgreeJoinSubmit', params)
  },
  // 办理中历史申请调整
  groupChangeList (params) {
    return post('/suggest/groupChangeList', params)
  },
  // 提案委跟踪办理列表
  allTrackSuggestList (params) {
    return post('/suggest/allTrackSuggestList', params)
  },
  // 提案委申请延期列表
  flowDelayList (params) {
    return post('/suggest/flowDelayList', params)
  },
  // 提案委申请调整办理单位列表
  flowBackList (params) {
    return post('/suggest/flowBackList', params)
  },
  // 申请调整办理单位审查
  auditExchangeTransact (params) {
    return post('/suggest/auditExchangeTransact', params)
  },
  // 申请延期审查
  flowDelayAudit (params) {
    return post('/suggest/flowDelayAudit', params)
  },
  // 办理单位申请跟踪办理
  requestTractAnswer (params) {
    return post('/suggest/requestTractAnswer', params)
  },
  // 办理单位跟踪办理列表
  groupTrackSuggestList (params) {
    return post('/suggest/groupTrackSuggestList', params)
  },
  // 提案委审查跟踪办理
  auditRequestTractAnswer (params) {
    return post('/suggest/auditRequestTractAnswer', params)
  },
  // 管理员/提案委 真实删除联名关系
  realDeleteJoinUser (params) {
    return post('/suggest/realDeleteJoinUser', params)
  },
  // 管理员/提案委 添加联名关系
  addAgreeJoinUser (params) {
    return post('/suggest/addAgreeJoinUser', params)
  },
  // 管理员修改办理单位
  superEditTransactGroup (params) {
    return post('/suggest/superEditTransactGroup', params)
  },
  // 设置/取消 重点提案/公开提案/优秀提案
  batchUpdateSuggest (params) {
    return post('/suggest/batchUpdateSuggest', params)
  },
  // 超管获取办理单位列表
  flowTransactList (params) {
    return post('/suggest/flowTransact/list', params)
  },
  // 超管获取办理单位列表详情
  flowTransactInfo (params) {
    return post(`/suggest/flowTransact/info/${params}`)
  },
  // 超管删除办理单位
  flowTransactDel (params) {
    return post(`/suggest/flowTransact/del/${params}`)
  },
  // 超管获取答复件列表
  answerList (params) {
    return post('/suggest/answerList', params)
  },
  // 可添加办理答复件的办理单位
  answerTransactList (params) {
    return post('/suggest/answerTransactList', params)
  },
  // 超管保存和编辑答复件
  superSaveFlowAnswer (params) {
    return post('/suggest/superSaveFlowAnswer', params)
  },
  // 超管删除答复件
  superDeleteFlowAnswer (params) {
    return post('/suggest/superDeleteFlowAnswer', params)
  },
  // 可添加跟踪办理答复件的办理单位
  tractAnswerTransactList (params) {
    return post('/suggest/tractAnswerTransactList', params)
  },
  // 提案word批量导出数据
  wordExport (params) {
    return post('/suggest/wordExport', params)
  },
  // 根据条件查询ids
  findQueryIds (params) {
    return post('/suggest/findQueryIds', params)
  },
  // 导出word的答复件
  exportReplyFile (params) {
    return filedownload('/suggest/exportFlowAnswerBySuggest?', params, 'arraybuffer')
  },
  // 统计分析
  suggestCount (params) {
    return post('/suggest/suggestCount', params)
  },
  // 办理中催办查看
  batchUrgeView (params) {
    return post('/suggest/batchUrgeView', params)
  },
  // 办理中催办答复
  batchUrgeAnswer (params) {
    return post('/suggest/batchUrgeAnswer', params)
  },
  // 催办提案办理评价(已答复建议催代表)
  batchUrgeEvaluate (params) {
    return post('/suggest/batchUrgeEvaluate', params)
  },
  // 发送短信
  submissionRelateUser (params) {
    return post('/suggest/submissionRelateUser', params)
  },
  // 导出办理单位状态
  importGroupStatusCount (params) {
    exportFile('/suggest/importGroupStatusCount?', params)
  },
  // 退回人大交办
  batchBackAssign (params) {
    return post('/suggest/batchBackAssign', params)
  },
  // 案号对调
  exchangeCustomNum (params) {
    return post('/suggest/exchangeCustomNum', params)
  },
  // 单位所有建议
  allTransactSuggestList (params) {
    return post('/suggest/allTransactSuggestList', params)
  },
  // 代表团审查批量审查通过到待审查
  representBatchAudit (params) {
    return post('/suggest/representBatchAudit', params)
  },
  // 建议待审查批量审查通过
  examineBatchPass (params) {
    return post('/suggest/examineBatchPass', params)
  },
  // 建议待复审批量审查通过
  reviewAuditBatchPass (params) {
    return post('/suggest/reviewAuditBatchPass', params)
  },
  // 建议待审定批量审查
  lastBatchAudit (params) {
    return post('/suggest/lastBatchAudit', params)
  },
  // 建议草稿批量提交
  representBatch (params) {
    return post('/suggest/represent/batch', params)
  }
}
export default suggest
