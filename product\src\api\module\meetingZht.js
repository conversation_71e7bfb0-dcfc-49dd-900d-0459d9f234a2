// 导入封装的方法
import {
  get,
  post,
  postform,
  _post,
  fileRequest,
  exportFile
} from '../http'
// const BASEURL = 'https://www.hnzhihuitong.com/zht_meet_szzx/a'
// const BASEURL = 'https://www.hnzhihuitong.com/zht_meet_zhuhaizx/a'
// const BASEURL = 'http://180.0.0.248:8081'
const meetingZht = {
  // 公用
  // 查询会议列表 top卡片位置
  meetMeeting: {
    meetOptions (params) { // 列表
      return post('/meetMeeting/options', params)
    },
    meetTree (params) { // 查询会议列表树
      return post('/meetMeeting/meetTree', params)
    }
  },
  // 所有会议
  allMeeting: {
    getRemoteMeeting (params) { // 获取远程视频信息
      return post('/meetMeeting/getRemoteMeeting', params)
    },
    createRemoteMeeting (params) { // 创建会议远程
      return post('/meetMeeting/createRemoteMeeting', params)
    },
    updateMeeetingById (params) { // 修改会议远程参会
      return post('/meetMeeting/updateMeeetingById', params)
    },
    getVideoMeetProvider (params) { // 获取可用的视频会议服务提供商
      return post('/meetMeeting/getVideoMeetProvider', params)
    },
    getVideoMeetProviders (params) { // 获取可用的视频会议服务提供商
      return post('/meetMeeting/getVideoMeetProvider', params)
    },
    zygetVideoMeetProviders (params) { // 获取可用的视频会议服务提供商
      return post('/zyMeeting/getVideoMeetProvider', params)
    },
    meetingList (params) { // 所有会议列表
      return post('/meetMeeting/list', params)
    },
    dels (params) { // 批量删除会议
      return post('/meetMeeting/dels', params)
    },
    putMeet (params) { // 上架/下架会议
      return post('/meetMeeting/putMeet', params)
    },
    meetType (params) { // 获取会议类型
      return post('/meetMeeting/meetType', params)
    },
    organize (params) { // 获取组织部门
      return get('/tree/list', params)
    },
    saveOtherPeople (params, header) { // 保存其他人员
      return post('/meetUser/saveMeetOtherUser', params, header)
    },
    loadOtherPeople (params) { // 获取其他人员
      return post('/meetUser/searchMeetUser', params)
    },
    loadApplyInform (params) { // 报名、签到通知请求
      return post('/meetMeeting/applyInform', params)
    },
    meetingServicesAdd (params) { // 新增、编辑会议服务
      return post('/meetServe/add', params)
    },
    meetingServicesDetail (params) { // 会议服务详情
      return post('/meetServe/info', params)
    },
    loadAttendanceCheck (params) { // 考勤查看
      return post('/meetMeeting/attendanceCheck', params)
    },
    loadExportCols (params) { // 获取可导出的字段
      return post('/meetMeeting/getExportFields', params)
    },
    exportMeetTable (params, headers) { // 导出会议列表
      return exportFile(`/meetMeeting/exportMeet?${params}`, headers)
    }
  },
  // 会务组
  getGroup: {
    findMeetGroupList (params) { // 会务组列表
      return post('/meetmeetingsgroup/findMeetGroupList', params)
    },
    addMeetGroup (params) { // 新增、修改
      return _post('/meetmeetingsgroup/addMeetGroup', params)
    },
    delsMeetGroup (params) { // 删除
      return post('/meetmeetingsgroup/delsMeetGroup', params)
    },
    findPointTree (params) { // 所管辖选人机构列表
      return post('/meetPointTree/findPointTree', params)
    },
    findPointTreeUsers (params) {
      return post('/meetPointTree/findPointTreeUsers', params)
    },
    meetAttendUser (params) { // 新增人员
      return post('/meetPointTree/meetAttendUser', params)
    }
  },
  // 人员分组
  meetGroup: {
    groupList (params) { // 列表
      return post('/meetGroup/groupList', params)
    },
    importTemplate (params) { // 导出人员分组模版
      return exportFile('/meetGroup/importTemplate', params)
    },
    import (params, data) { // 导入人员分组excel
      return postform(`/meetGroup/import?relationId=${params}`, data)
    },
    editGroup (params, headers) { // 编辑新增
      return post('/meetGroup/editGroup', params, headers)
    },
    delete (params, headers) { // 删除
      return post('/meetGroup/delete', params, headers)
    },
    export (params, headers) { // 导出固定人员分组模版
      return exportFile('/meetGroup/export', params)
    }
  },
  // 数据统计
  dataStatistics: {
    // 报名统计
    getApplyStatistics (params) {
      return post('/meetMeeting/applyStatistics', params)
    },
    // 出席率
    getAttendance (params) {
      return post('/meetMeeting/attendance', params)
    }
  },
  // 报名管理
  meetSignUp: {
    applyLeaveCount (params) { // 报名请假卡片数据统计
      return post('/meetApplyLeave/countApplyLeaveData', params)
    },
    applyLeaveList (params) { // 报名请假列表
      return post('/meetApplyLeave/getApplyLeaveList', params)
    },
    apply (params) { // 报名
      return post('/meetApplyLeave/applyByAdministrators', params)
    },
    leave (params) { // 请假
      return post('/meetApplyLeave/leaveByAdministrators', params)
    },
    exportList (params) { // 导出报名请假列表
      return exportFile(`/meetApplyLeave/exportMeetApplyLeave?${params}`)
    },
    leaveMeetList (params) { // 请假弹框会议树形数据
      return post('/meetMeeting/leaveMeetList', params)
    },
    getLeaveInfo (params) { // 获取请假审批详情
      return post('/meetApplyLeave/getLeaveInfoByAdministratorsAndExamine', params)
    },
    getLeaveInfos (params) { // 获取请假详情
      return post('/meetApplyLeave/getLeaveInfoByAdministrators', params)
    },
    leaveApproval (params) { // 请假审批
      return post('/meetApplyLeave/leaveApprovalByAdministrators', params)
    },
    getApplyLeaveInfo (params) { // 获取处理详情
      return post('/meetApplyLeave/getApplyLeaveInfoList', params)
    }
  },
  // 签到管理
  meetSign: {
    signList (params) { // 列表
      return post('/meetSign/list', params)
    },
    judgeHaveSonMeet (params) { // 判断父子会
      return post('/meetSign/judgeHaveSonMeet', params)
    },
    signTypeList (params) { // 签到主题列表
      return post('/meetSignType/list', params)
    },
    exportCode (params) { // 签到二维码
      return post('/meetSign/exportCode', params)
    },
    adminSign (params) { // 签到
      return post('/meetSign/adminSign', params)
    },
    adminNotSign (params) { // 取消签到
      return post('/meetSign/adminNotSign', params)
    },
    updateSignType (params) { // 增删改签到类型
      return post('/meetSignType/updateSignType', params)
    },
    updateSignRange (params, headers) { // 新增编辑会议签到范围
      return post('/meetSignUser/updateSignRange', params, headers)
    },
    findMeetSignUserRange (params) { // 查询会议签到范围
      return get('/meetSignUser/findMeetSignUserRange', params)
    },
    export (params) { // 导出
      return exportFile('/meetSign/export', params)
    },
    // 二维码下载
    generateWord (params) { // 导出
      return exportFile('/meetSign/generateWord', params)
    },
    // 设置代理人
    adminAgent (params) {
      return post('/meetSign/adminAgent', params)
    }
  },
  // 通知模板
  meetTemplate: {
    list (params) { // 列表
      return post('/meetMessageTemplate/list', params)
    },
    add (params) { // 增通知模板
      return post('/meetMessageTemplate/add', params)
    },
    edit (params) { // 改通知模板
      return post('/meetMessageTemplate/edit', params)
    },
    id (params) { // get info
      return post(`/meetMessageTemplate/info/${params}`)
    },
    dels (params) {
      return post('/meetMessageTemplate/dels', params)
    },
    postSearch (params) {
      return post('/meetUser/searchMeetUser', params)
    }
  },
  // pc模板
  meetModule: {
    moduleList (params) { // 列表
      return post('/meetPcModule/list', params)
    },
    moduleEdit (params) { // 增删改pc模板
      return post('/meetPcModule/editModule', params)
    },
    meetPcModuleList (params) { // 针对会议入口页面
      return post('/meetPcModule/meetPcModuleList', params)
    },
    templateRoles (params) { // 获取角色
      return post('/meetAppTemplate/templateRoles', params)
    },
    meetTypeList (params) { // 获取会议类型
      return post('/meetAppTemplate/meetTypeList', params)
    },
    findMeetPcModulesByType (params) { // PC端模块列表（分会议流程）
      return post('/meetPcModule/findMeetPcModulesByType', params)
    },
    meetPcModuleTypesAdd (params, headers) { // 新增pc端模块化关联会议类型
      return post('/meetPcModuleType/add', params, headers)
    },
    meetPcModuleTypesInfo (params) { // 获取PC端模块关联详情
      return post('/meetPcModuleType/info', params)
    }
  },
  // 分组管理
  groupManagement: {
    list (params) { // 列表
      return post('/meetSubgroup/list', params)
    },
    addGroup (params) { // 新增会议-分组管理
      return post('/meetSubgroup/add', params)
    },
    editGroup (params) { // 修改会议-分组管理
      return post('/meetSubgroup/edit', params)
    },
    groupDdels (params) { // 删除会议-分组管理
      return post('/meetSubgroup/dels', params)
    },
    editSort (params) { // 修改序号
      return post('/meetSubgroup/editSort', params)
    },
    groupInfo (id) { // 分组管理详情
      return get(`/meetSubgroup/info/${id}`)
    }
  },
  // 会议小助手
  meetAssistant: {
    list (params) { // 列表
      return post('/meetAssistant/list', params)
    },
    edit (params, headers) { // 小助手关键词
      return post('/meetAssistant/edit', params, headers)
    }
  },
  // 系统外人员管理
  personnelMng: {
    personnelList (params) { // 列表
      return post('/meetExtraPeople/list', params)
    },
    generalAdd (url, params) { // 新增、编辑
      return post(url, params)
    },
    fileImg (params) {
      return postform('/file/uploadimg', params)
    },
    personnelAdd (params) { // 新增
      return post('/meetExtraPeople/add', params)
    },
    personnelDels (params) { // 删除
      return post('/meetExtraPeople/dels', params)
    },
    personnelInfo (id) { // 详情
      return get(`/meetExtraPeople/info/${id}`)
    },
    personnelEdit (params) { // 编辑
      return post('/meetExtraPeople/edit', params)
    },
    personnelTemplate () { // 下载导入模板
      return exportFile('/meetExtraPeople/importTemplate')
    },
    personnelImport (data) { // 导入
      return postform('/meetExtraPeople/import', data)
    }
  },
  // 事务安排字段配置
  transactionMng: {
    transactionList (params) { // 列表
      return get('/meetPlanConfig/list', params)
    },
    transactionlEdit (params) { // 编辑
      return post('/meetPlanConfig/edit', params)
    },
    transactionUseList () { // 已启用列表
      return get('/meetPlanConfig/getTree')
    },
    meetPlanTree (params) { // 查询启用的事务安排配置
      return get('/meetPlanConfig/getTree', params)
    }
  },
  // 事务安排
  transactionSchedule: {
    transactionList (params) { // 列表
      return get('/meetPlan/list', params)
    },
    transactionlDels (params) { // 删除
      return get('/meetPlan/dels', params)
    },
    transactionlSend (params) { // 发送通知
      return _post('/meetPlan/sendMessage', params)
    },
    transactionlTemplate (params) { // 下载导入模板
      return exportFile('/meetPlan/importTemplate', params)
    },
    transactionlImport (params, data) { // 导入
      return postform(`/meetPlan/import?meetId=${params.meetId}&configId=${params.configId}`, data)
    },
    meetUserWordList (data) { // 导入
      return postform('/meetUser/wordList', data)
    },
    addAndEditMeetPlan (params) { // 新增/修改会议-事务安排录入数据
      return get('/meetPlanSummary/addAndEdit', params)
    },
    getPlanSummaryDetail (params) { // 事务安排录入数据详情
      return post('/meetPlanSummary/getPlanSummaryDetail', params)
    }
  },
  // 会议回执
  meeetingReceipt: {
    receiptList (params) { // 会议回执列表
      return post('/meetReceipt/list', params)
    },
    receiptStatistical (params) { // 数据统计
      return get('/meetReceipt/getReceiptCount', params)
    },
    receiptReply (params) { // 回复回执
      return _post('/meetReceipt/replyReceipt', params)
    },
    receiptExport (params) { // 导出会议回执
      return exportFile('/meetReceipt/export', params)
    },
    exportReceipt (params) { // 导出会议回执
      return exportFile('/meetReceipt/exportReceipt', params)
    },
    receiptOptionList (params) { // 回执选项列表
      return get('/meetReceiptOption/list', params)
    },
    receiptOptionAdd (params) { // 新增回执选项
      return post('/meetReceiptOption/add', params)
    },
    receiptOptionEdit (params) { // 编辑回执选项
      return post('/meetReceiptOption/edit', params)
    },
    receiptOptionDels (params) { // 删除回执选项
      return post('/meetReceiptOption/dels', params)
    },
    receiptOptionDetail (id) { // 回执选项详情
      return get(`/meetReceiptOption/info/${id}`)
    },
    receiptScope (params) { // 已选回执范围
      return post('/meetReceiptUser/list', params)
    },
    receiptScopeAdd (params) { // 设置回执范围
      return post('/meetReceiptUser/add', params)
    },
    replyInfos (params) { // 回执详情
      return post('/meetAppReceipt/replyInfos', params)
    }
  },
  // 会议决议反馈
  meeetingReceipts: {
    receiptList (params) { // 会议决议列表
      return post('/meetResolution/list', params)
    },
    receiptStatistical (params) { // 数据统计
      return get('/meetResolution/getResolutionCount', params)
    },
    receiptReply (params) { // 回复决议
      return _post('/meetResolution/replyResolution', params)
    },
    receiptExport (params) { // 导出会议决议
      return exportFile('/meetResolution/export', params)
    },
    exportReceipt (params) { // 导出会议决议
      return exportFile('/meetResolution/exportResolution', params)
    },
    receiptOptionList (params) { // 决议项列表
      return get('/meetResolutionOption/list', params)
    },
    receiptOptionAdd (params) { // 新增决议选项
      return post('/meetResolutionOption/add', params)
    },
    receiptOptionEdit (params) { // 编辑决议选项
      return post('/meetResolutionOption/edit', params)
    },
    receiptOptionDels (params) { // 删除决议选项
      return post('/meetResolutionOption/dels', params)
    },
    receiptOptionDetail (id) { // 决议选项详情
      return get(`/meetResolutionOption/info/${id}`)
    },
    receiptScope (params) { // 已选决议范围
      return post('/meetResolutionUser/list', params)
    },
    receiptScopeAdd (params) { // 设置决议范围
      return post('/meetResolutionUser/add', params)
    },
    replyInfos (params) { // 决议详情
      return post('/meetAppResolution/replyInfos', params)
    }
  },
  // 会议材料
  meetMaterial: {
    meetfileList (params) { // 列表
      return post('/meetFile/list', params)
    },
    materialAdd (params) { // 新增材料
      return post('/fileUploadController/uploadFile', params)
    },
    materialTypeList (params) { // 材料类型列表
      return post('/meetFile/fileType', params)
    },
    materialDels (params) { // 删除
      return post('/meetFile/dels', params)
    },
    meetfileDownload (params, text) { // 下载
      return fileRequest('/fileUploadController/download', params, text)
    },
    findMeetFileControl (params) { // 文件已授权人员查询
      return post('/meetFileControl/findMeetFileControl', params)
    },
    saveMeetFileControl (params) { // 授权
      return post('/meetFileControl/saveMeetFileControl', params)
    },
    editMeetTitle (params) {
      return post(`/meetFile/editMeetTitle?${params}`)
    },
    meetFileUpdateSort (params) { // 文件序号修改
      return post('/meetFile/updateSort', params)
    }
  },
  // 新增会议
  meetAdd: {
    meetmeetingtempList (params) { // 模板列表
      return post('/meetMeetingTemp/list', params)
    },
    exportMeetGuide (params) { // 一键生成会务指南
      return exportFile('/meetMeeting/exportMeetGuide', params)
    },
    meetmeetingtempInfo (id) { // 模板详情
      return post(`/meetMeetingTemp/info/${id}`)
    },
    meetMeetingInit (params) { // 初始化id
      return post('/meetMeeting/init', params)
    },
    meetmeetingAdd (params) { // 新增会议
      return post('/meetMeeting/add', params)
    },
    meetmeetingEdit (id) { // 编辑会议
      return post(`/meetMeeting/edit/${id}`)
    },
    meetmeetingInfo (id) { // 会议详情
      return post(`/meetMeeting/info/${id}`)
    },
    meetAttendUser (params) { // 新增人员
      return post('/meetPointTree/meetAttendUser', params)
    },
    meetUserAdd (params) { // 新增人员
      return _post('/meetUser/saveMeetUser', params)
    },
    meetFileAdd (params) { // 新增材料
      return post('/meetFile/add', params)
    },
    meetFileeEdit (params) { // 编辑材料
      return post('/meetFile/edit', params)
    },
    meetMeetingTempAdd (params) { // 新增会议模板
      return post('/meetMeetingTemp/add', params)
    },
    meetMeetingTempEdit (params) { // 编辑会议模板
      return post('/meetMeetingTemp/edit', params)
    },
    meetTypeList (params) { // 会议类型
      return post('/meetMeeting/meetType', params)
    },
    unitList (params) { // 组织部门
      return post('/tree/list', params)
    },
    meetPlaceList (params) { // 会议室
      return post('/meetPlace/listData', params)
    },
    checkMeetPlace (params) { // 会议室是否冲突
      return post('/meetMeeting/checkMeetPlace', params)
    },
    checkMeetName (params) { // 会议名称是否冲突
      return post('/meetMeeting/checkMeetName', params)
    },
    putMeet (params) { // 发布接口
      return post('/meetMeeting/putMeet', params)
    }
  },
  // 发送通知
  sendInforms: {
    searchMeetUser (params) { // 查询参会人员
      return post('/meetUser/searchMeetUser', params)
    },
    sendMessage (params) { // 发送
      return _post('/meetMessageLog/sendMessage', params)
    },
    sendSMS (params) { // 修改导入住宿安排信息发送短信状态
      return post('/meetImportStation/sendSMS', params)
    }
  },
  // 发送阅读记录
  sendRecords: {
    getMessageLog (params) { // 列表
      return post('/meetMessageLog/getMessageLog', params)
    },
    getMessageCount (params) { // 统计
      return post('/meetMessageLog/getMessageCount', params)
    }
  },
  // 阅读记录
  readRecords: {
    readingRecordsList (params) { // 列表
      return post('/meetBrowseLog/readingList', params)
    },
    getReadingCount (params) { // 统计
      return post('/meetBrowseLog/getReadingCount', params)
    }
  },
  // app模板
  appTemplate: {
    moduleList (params) { // 列表
      return post('/meetAppModule/list', params)
    },
    moduleAdd (params) {
      return post('/meetAppModule/add', params)
    },
    moduleEdit (params) {
      return post('/meetAppModule/edit', params)
    },
    moduleDel (params) { // del
      return post(`/meetAppModule/del/${params}`)
    },
    moduleData () {
      return post('/meetAppModule/moduleData')
    },
    dictionaryList (params) {
      return post('/dictionary/list', params)
    },
    meetPlanConfigList (params) {
      return post('/meetPlanConfig/list', params)
    },
    // app模板
    templateList (params) {
      return post('/meetAppTemplate/list', params)
    },
    templateAdd (params) {
      return post('/meetAppTemplate/add', params)
    },
    templateEdit (params) {
      return post('/meetAppTemplate/edit', params)
    },
    templateInfo (params) {
      return post(`/meetAppTemplate/info/${params}`)
    },
    templateDel (params) {
      return post(`/meetAppTemplate/del/${params}`)
    }
  },
  // 会议模板
  meetingTemplate: {
    meetingTemplateDels (params) { // 删除
      return post('/meetMeetingTemp/dels', params)
    },
    meetingTemplateEdit (params) { // 编辑
      return post('/meetMeetingTemp/edit', params)
    }
  },
  // 选人
  meetPointTree: {
    importTemplate (data) { // 选人组件导入数据
      return postform('/meetPointTree/importTemplate', data)
    },
    exportTemplate (params) { // 选人组件导出模版文件
      return exportFile('/meetPointTree/exportTemplate', params)
    },
    meetAttendUser (params) { // 选人组件导出模版文件
      return post(`/meetPointTree/meetAttendUser?meetId=${params}`)
    },
    extraPeopleParent (data) { // 选人组件单位
      return postform('/meetPointTree/extraPeopleParent', data)
    },
    extraPeopleChild (data, headers) { // 选人组件人
      return postform('/meetPointTree/extraPeopleChild', data, headers)
    }
  },
  // 会议室管理
  meetingRoomMng: {
    meetRoomList (params) { // 会议室列表
      return post('/meetPlace/list', params)
    },
    meetRoomEdit (params) { // 会议室编辑
      return post('/meetPlace/save', params)
    },
    dels (params) { // 会议室删除
      return post('/meetPlace/deleteBatch', params)
    },
    saveSeatInfo (params) { // 保存会议室座位信息
      return post('/meetPlace/saveLayout', params)
    },
    loadSeatInfo (params) { // 加载会议室座位信息
      return post('/meetPlace/getLayout', params)
    }
  },
  // 住地管理
  meetingPlaceMng: {
    meetPlaceList (params) { // 住地列表
      return post('/meetHotel/list', params)
    },
    meetPlaceEdit (params) { // 住地编辑
      return post('/meetHotel/save', params)
    },
    dels (params) { // 住地删除
      return post('/meetHotel/deleteBatch', params)
    },
    saveRoomInfo (params) { // 保存住地房间信息
      return post('/meetHotel/saveLayout', params)
    },
    loadRoomInfo (params) { // 加载住地房间信息
      return post('/meetHotel/getLayout', params)
    }
  },
  // 会议排座
  meetingSeat: {
    getType (params) { // 获取会议排座方式信息
      return post('/meetSeatMode/get', params)
    },
    saveType (params) { // 保存会议排座方式信息
      return post('/meetSeatMode/save', params)
    },
    getSeatFile (params) { // 获取会议排座文件
      return post('/meetSeatMode/getFile', params)
    },
    delSeatFile (params) { // 删除会议排座文件
      return post('/meetSeatMode/deleteFile', params)
    },
    materialAdd (params) { // 上传会议排座文件
      return postform('/fileUploadController/uploadFile', params, { 'Content-Type': 'multipart/form-data' })
    },
    materialEdit (params) { // 修改会议排座文件状态
      return post('/meetFile/edit', params)
    },
    findBusinessConfig (params) { // 获取会议业务配置
      return _post('/meetBusinessConfig/findBusinessConfig', params)
    },
    findBusinessConfigDetail (params) { // 获取会议业务配置详情
      return _post('/meetBusinessConfig/findBusinessConfigDetail', params)
    },
    batchSaveBusinessConfig (params) { // 批量保存会议业务配置
      return _post('/meetBusinessConfig/batchSaveBusinessConfig', params)
    },
    getEnumList (params) { // 获取枚举列表
      return _post('/meetBusinessConfig/getEnumList', params)
    },
    findMeetSeatUserList (params) { // 获取会议排座人员信息列表
      return _post('/meetSeat/findMeetSeatUserList', params)
    },
    getMeetSeatLayout (params) { // 根据会议id获取会议排座布局信息
      return _post('/meetSeat/findMeetSeatAndPlaceLayout', params)
    },
    batchSaveMeetSeat (params) { // 批量保存会议排座信息
      return _post('/meetSeat/batchSaveMeetSeat', params)
    },
    importemplate (params) { // 下载会议排座的导入模板
      exportFile('/meetSeat/getImportTemplate', params)
    },
    importMeetSeat (params) { // 导入会议排座
      return postform('/meetSeat/importMeetSeat', params)
    },
    importMeetSeatMap (params) { // 导入会议排座-座位图
      return postform('/meetSeat/importMeetSeatByMap', params)
    }
  },
  // 住宿安排
  meetingRoom: {
    getType (params) { // 获取会议住地安排方式信息
      return post('/meetStationMode/get', params)
    },
    saveType (params) { // 保存会议住地安排方式信息
      return post('/meetStationMode/save', params)
    },
    getList (params) { // 获取住宿安排信息分页列表
      return post('/meetImportStation/list', params)
    },
    deleList (params) { // 获取界别下拉框信息
      return post('/meetImportStation/deleList', params)
    },
    stationList (params) { // 获取住地下拉框信息
      return post('/meetImportStation/stationList', params)
    },
    dels (params) { // 删除住地信息
      return post('/meetImportStation/dels', params)
    },
    pushApp (params) { // 推送导入住宿安排信息至APP
      return post('/meetImportStation/pushAPP', params)
    },
    getTemplate (params) { // 获取住宿安排的导入模板
      return exportFile('/meetImportStation/getTemplate', params)
    },
    stationImport (params, data) { // 导入
      return postform(`/meetImportStation/importStation?relationId=${params}`, data)
    }
  },
  // 投票表决
  speak: {
    // 投票统计
    getMeetVoteStatistics (params) {
      return post('/meetvote/getMeetVoteStatistics', params)
    },
    // 保存发送范围人员
    postSubUser (params) {
      return _post('/meetvoteuser/saveVoteUser', params)
    },
    // 初始化表决新增ID
    getInit (params) {
      return post('/meetvote/init', params)
    },
    // 获取关联会议
    getMeetVoteTree (params) {
      return post('/meetMeeting/meetVoteTree', params)
    },
    // 新增表决主题
    postAdd (params) {
      return post('/meetvote/add', params)
    },
    // 编辑表决主题
    postEdit (params) {
      return post('/meetvote/edit', params)
    },
    // 获取主题详情
    getInfo (params) {
      return post(`/meetvote/info/${params}`)
    },
    // 获取主题发送范围回显
    getUserCallback (params) {
      return post('/meetvoteuser/echoVoteUser', params)
    },
    // 获取投票表决列表
    postList (params) {
      return post('/meetvote/list', params)
    },
    // 表决发布、取消发布
    postToRelease (params) {
      return post('/meetvote/toRelease', params)
    },
    // 删除表决主题
    dels (params) {
      return post('/meetvote/dels', params)
    },
    // 表决题目新增
    postMeetvotetitle (params) {
      return post('/meetvotetitle/add', params)
    },
    // 表决题目编辑
    postEdits (params) {
      return post('/meetvotetitle/edit', params)
    },
    // 获取题目详情
    postInfo (params) {
      return post(`/meetvotetitle/info/${params}`)
    },
    // 删除题目
    postDels (params) {
      return post('/meetvotetitle/dels', params)
    },
    // 获取题目列表
    getMeetvotetitle (params) {
      return post('/meetvotetitle/list', params)
    }
  },
  // 大会发言
  meetsolicitation: {
    // 下载发言稿
    getExportWord (params) {
      return exportFile('/meetsolicitationspeck/exportWord', params)
    },
    // 发言列表下载目录
    getExportZip (params) {
      return exportFile('/meetsolicitationspeck/exportZip', params)
    },
    // 修改采用状态
    getEditSate (params) {
      return post('/meetsolicitationspeck/editSate', params)
    },
    // 管理员获取征集通知列表
    getListst (params) {
      return post('/meetsolicitation/adminList', params)
    },
    // 普通用户获取征集通知列表
    getList (params) {
      return post('/meetsolicitation/list', params)
    },
    // 征集通知保存
    postAdd (params) {
      return post('/meetsolicitation/add', params)
    },
    // 编辑征集通知
    postEdit (params) {
      return post('/meetsolicitation/edit', params)
    },
    // 删除征集通知
    postDels (params) {
      return post('/meetsolicitation/dels', params)
    },
    // 获取征集通知详情
    getInfo (params) {
      return post(`/meetsolicitation/info/${params}`)
    },
    // 保存征集人员
    postUser (params) {
      return _post('/meetsolicitation/saveSolicitationUser', params)
    },
    // 回显征集人员
    echoSolicitationUser (params) {
      return post('/meetsolicitation/echoSolicitationUser', params)
    },
    // 发布、取消发布
    toRelease (params) {
      return post('/meetsolicitation/toRelease', params)
    },
    // 补录发言
    postAdds (params) {
      return post('/meetsolicitationspeck/add', params)
    },
    // 全部发言列表
    getListsts (params) {
      return post('/meetsolicitationspeck/adminList', params)
    },
    // 我的发言列表
    getLists (params) {
      return post('/meetsolicitationspeck/list', params)
    },
    // 删除发言
    postDelst (params) {
      return post('/meetsolicitationspeck/dels', params)
    },
    // 获取发言详情
    getInfos (params) {
      return post(`/meetsolicitationspeck/info/${params}`)
    },
    // 用户获取发言详情
    getUserInfot (params) {
      return post(`/meetsolicitationspeck/info/${params}`)
    },
    // 删除发言
    pollDels (params) {
      return post('/meetsolicitationspeck/list', params)
    },
    // 获取发言人信息
    getUsers (params) {
      return post('/meetsolicitationspeck/getSolicitationUser', params)
    },
    // 获取用户信息
    getUserInfo (params) {
      return post('/meetsolicitationspeck/getUserInfo', params)
    }
  },
  // 设备管理
  meetingDeviceManagement: {
    // 列表
    meetingDeviceList (params) {
      return get('/meetMeetingEquipment/getmeet', params)
    },
    // 新增
    addMeetingDevice (params) {
      return post('/meetMeetingEquipment/add', params)
    },
    // 编辑
    editMeetingDevice (params) {
      return post('/meetMeetingEquipment/edit', params)
    },
    // 删除
    delsMeetingDevice (params) {
      return post('/meetMeetingEquipment/dels', params)
    }
  },

  title: {
    myTitleDetail (params) {
      return post('/meetspeech/mytitle', params)
    },
    reprentList (params) {
      return post('/meetspeech/users', params)
    },
    titleList (params) { // 征集题目列表
      return post('/meetspeech/subjectlist', params)
    },
    myTitleList (params) { // 征集题目列表
      return post(`/lzt/meetspeech/subjectlist?id=${params}`)
    },
    noticeList (params) { // 征集通知列表
      return post('/meetspeechrequest/list', params)
    },
    feedback (params) {
      return post('/meetspeech/batchaudit', params)
    },
    add (params) {
      return post('/meetspeech/addsubject', params)
    }
  },
  statement: {
    statementList (params) { // 所有发言列表
      return post('/meetspeech/list', params)
    },
    myStatementList (params) { // 我的发言列表
      return post('/meetspeech/myspeechs', params)
    },
    myStatementDetail (params) { // 我的发言详情
      return post(`/meetspeech/myspeech?id=${params}`)
    },
    openList (params) { // 公开发言列表
      return post('/meetspeech/listbypublic', params)
    },
    add (params) { // 新增发言
      return post('/meetspeech/add', params)
    },
    supplement (params) { // 补录发言
      return post('/meetspeech/supplement', params)
    },
    firstCheck (params) { // 初审发言
      return post('/meetspeech/firstTrial', params)
    },
    groupcheck (params) { // 小组审核发言
      return post('/meetspeech/groupcheck', params)
    },
    deputyleadercheck (params) { // 副组长审核发言
      return post('/meetspeech/deputyleadercheck', params)
    },
    leadercheck (params) { // 领导审核发言
      return post('/meetspeech/leadercheck', params)
    },
    dowloadStatement (params, text) {
      return exportFile('/meetspeech/dowload', params)
    },
    exportStatement (params) {
      return exportFile('/meetspeech/export', params)
    },
    edit (params) { // 编辑发言
      return post('/meetspeech/edit', params)
    },
    setpublic (params) { // 批量公开发言
      return post('/meetspeech/setpublic', params)
    },
    unpublic (params) { // 批量取消公开发言
      return post('/meetspeech/unpublic', params)
    },
    info (params) { // 发言详情
      return post(`/meetspeech/info/${params}`)
    },
    meetspeechrequestinfo (params) { // 通知详情
      return post(`/meetspeechrequest/info/${params}`)
    },
    dels (params) { // 批量删除发言
      return post('/meetspeech/dels', params)
    },
    del (params) { // 单个删除发言
      return post('/meetspeech/del', params)
    },
    distribute (params) { // 分发发言
      return post('/meetspeech/distribute', params)
    },
    firsttrials (params) { // 初审人选项列表
      return post('/meetspeechrequest/newFirsttrial', params)
    },
    firsttrial (params) { // 初审人选项列表
      return post('/meetspeechrequest/firsttrial', params)
    },
    noticeList (params) { // 征集通知列表
      return post('/meetspeechrequest/list', params)
    },
    sendMessageSpeech (params) { // 发送征集通知
      return post('/meetMessageLog/sendMessageSpeech', params, { 'Content-Type': 'application/json;charset=UTF-8' })
    },
    noticeAdd (params) { // 新增征集通知
      return post('/meetspeechrequest/add', params, { 'Content-Type': 'application/json;charset=UTF-8' })
    },
    noticeEdit (params) { // 修改征集通知
      return post('/meetspeechrequest/edit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
    },
    noticeDels (params) { // 征集通知列表
      return post('/meetspeechrequest/dels', params)
    },
    noticeInfo (params) { // 征集通知详情
      return post(`/meetspeechrequest/info/${params}`)
    },
    noticePub (params) { // 征集通知详情
      return post('/meetspeechrequest/publish', params)
    },
    noticeUnpub (params) { // 征集通知详情
      return post('/meetspeechrequest/unpublish', params)
    },
    groupOpt (params) { // 会议组别
      return post('/meetgroup/groupList', params)
    },
    getSpeechRequestTheme (params) { // 获取征集通知下的参考主题
      return post('/meetspeechrequest/getSpeechRequestTheme', params)
    },
    findMyMeetStatements (params) { // 我的发言
      return post('/meetStatement/findMyMeetStatements', params)
    },
    meetStatementdels (params) { // 删除我的发言
      return post('/meetStatement/dels', params)
    },
    meetStatementinfo (params) { // 发言详情
      return post('/meetStatement/info/' + params)
    },
    meetStatementAdd (params) { // 提交发言
      return post('/meetStatement/add', params)
    },
    meetStatementEdit (params) { // 提交发言
      return post('/meetStatement/edit', params)
    },
    meetspeechrequestuserList (params) { // 委员征集通知列表
      return post('/meetspeechrequest/userList', params)
    }
  }
}
export default meetingZht
