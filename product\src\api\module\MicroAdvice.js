import { post, get } from '../http'

const MicroAdvice = {
  generalAdd (url, params) {
    return post(url, params)
  },
  // 单位列表
  microflowgroupList (params) {
    return post('/microflowgroup/list', params)
  },
  // 单位列表详情
  microflowgroupInfo (params) {
    return post(`/microflowgroup/info/${params}`)
  },
  // 单位列表
  microflowgroupDel (params) {
    return post('/microflowgroup/dels', params)
  },
  // 单位列表批量启用/禁用
  batchUpdateState (params) {
    return post('/microflowgroup/batchUpdateState', params)
  },
  // 办理单位用户列表
  microflowgroupUserList (params) {
    return post('/microflowgroup/userList', params)
  },
  // 办理单位树选择列表
  microflowgroupChooseList (params) {
    return post('/microflowgroup/chooseList', params)
  },
  // 微建议管理列表
  microadviceList (params) {
    return post('/microadvice/list?', params)
  },
  // 微建议详情
  microadviceInfo (params) {
    return post(`/microadvice/info/${params}`)
  },
  // 新增微建议
  microadviceAdd (params) {
    return post('/microadvice/add', params)
  },
  // 微建议编辑(管理员)
  microadviceEdit (params) {
    return post('/microadvice/edit?', params)
  },
  // 微建议编辑(管理员)
  microadviceMemberEdit (params) {
    return post('/microadvice/memberEdit?', params)
  },
  // 删除微建议
  microadviceDel (params) {
    return post(`/microadvice/del/${params}`)
  },
  // 批量删除微建议
  microadviceDels (params) {
    return post('/microadvice/dels/', params)
  },
  // 一键还原待审核(管理员)
  microadviceBatchToAudit (params) {
    return post(`/microadvice/batchToAudit?ids=${params}`)
  },
  // 一键还原待审核(管理员)
  microadviceBatchToUnreply (params) {
    return post(`/microadvice/batchToUnreply?ids=${params}`)
  },
  // 提交交办
  microadviceTransact (params) {
    return post('/microadvice/transact?', params)
  },
  // 管理员审核微建议
  microadviceAudit (params) {
    return post('/microadvice/audit?', params)
  },
  // 申诉审核(pc和app)
  microadviceAuditChange (params) {
    return post('/microadvice/auditChange?', params)
  },
  // 获取当前地区可以微建议的所属地区
  microadviceGetDataArea () {
    return post('/microadvice/getDataAreas')
  },
  // 获取上级地区信息
  microadviceGetPreArea (params) {
    return post('/area/getPreArea?', params)
  },
  // 获取所有下级地区
  microadviceGetUnderArea (params) {
    return post('/area/getUnderArea?', params)
  },
  // 领办委员回复(pc和app)
  microadviceMemberReply (params) {
    return post('/microadvice/memberReply?', params)
  },
  // 管理中心回复(pc和app)
  microadviceManageReply (params) {
    return post('/microadvice/manageReply?', params)
  },
  // 下级申请调整（pc和app）
  microadviceApplyChange (params) {
    return post('/microadvice/applyChange?', params)
  },
  // 上级退回
  microadviceTransactBack (params) {
    return post('/microadvice/transactBack?', params)
  },
  // 上报不予受理（pc和app）
  microadviceRefuseReceive (params) {
    return post('/microadvice/refuseReceive?', params)
  },
  // 协办人员管理(pc)
  microadviceManageJointly (params) {
    return post('/microadvice/manageJointly?', params)
  },
  // 委员测评（pc和app）
  microadviceAddEvaluate (params) {
    return post('/microadvice/addEvaluate?', params)
  },
  // 取消领办(管理员和委员通用)
  microadviceCancelCollar (params) {
    return post('/microadvice/cancelCollar?', params)
  },
  // 办理单位回复(pc和app)
  microadviceGroupReply (params) {
    return post('/microadvice/groupReply?', params)
  },
  // 我的微建议列表
  microadviceMyList (params) {
    return post('/microadvice/myList?', params)
  },
  // 我的微建议列表统计项
  microadviceMyListCount () {
    return get('/microadvice/myListCount')
  },
  // 微建议统计
  microadviceAdviceCount (params) {
    return post('/microadvice/adviceCount?', params)
  },
  // 微建议办理列表
  microadviceyGroupList (params) {
    return post('/microadvice/groupList?', params)
  },
  // 微建议办理列表统计项
  microadviceyGroupListCount (params) {
    return get('/microadvice/groupListCount')
  },
  // 编辑回复(pc)
  microflowreplyEdit (params) {
    return post('/microflowreply/edit?', params)
  },
  // 回复管理列表(pc)
  microflowreplyList (params) {
    return post('/microflowreply/list?', params)
  },
  // 导出word
  microadviceWordExport (params) {
    return post('/microadvice/wordExport?', params)
  }

}
export default MicroAdvice
