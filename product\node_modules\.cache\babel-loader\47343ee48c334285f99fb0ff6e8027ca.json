{"remainingRequest": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\jnzx\\product\\src\\views\\app-management\\committee-data\\components\\committeeDataDetails.vue?vue&type=template&id=672eeb96&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\jnzx\\product\\src\\views\\app-management\\committee-data\\components\\committeeDataDetails.vue", "mtime": 1762767300301}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "_s", "details", "title", "structureName", "createBy", "source", "publishDate", "attachmentList", "_l", "filter", "data", "moduleType", "item", "index", "key", "fileName", "on", "click", "$event", "FilePreview", "fileClick", "_e", "domProps", "innerHTML", "content", "staticRenderFns", "_withStripped"], "sources": ["D:/zy/xm/pc/jnzx/product/src/views/app-management/committee-data/components/committeeDataDetails.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"committeeDataDetails details\" }, [\n    _c(\"div\", { staticClass: \"details-title\" }, [_vm._v(\"详情\")]),\n    _c(\"div\", { staticClass: \"details-item-box\" }, [\n      _c(\"div\", { staticClass: \"details-item-title\" }, [\n        _c(\"div\", { staticClass: \"details-item-label\" }, [_vm._v(\"标题\")]),\n        _c(\"div\", { staticClass: \"details-item-value\" }, [\n          _vm._v(_vm._s(_vm.details.title)),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"details-item-column\" }, [\n        _c(\"div\", { staticClass: \"details-item\" }, [\n          _c(\"div\", { staticClass: \"details-item-label\" }, [\n            _vm._v(\"所属栏目\"),\n          ]),\n          _c(\"div\", { staticClass: \"details-item-value\" }, [\n            _vm._v(_vm._s(_vm.details.structureName)),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"details-item\" }, [\n          _c(\"div\", { staticClass: \"details-item-label\" }, [_vm._v(\"发布人\")]),\n          _c(\"div\", { staticClass: \"details-item-value\" }, [\n            _vm._v(_vm._s(_vm.details.createBy)),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"details-item-column\" }, [\n        _c(\"div\", { staticClass: \"details-item\" }, [\n          _c(\"div\", { staticClass: \"details-item-label\" }, [_vm._v(\"来源\")]),\n          _c(\"div\", { staticClass: \"details-item-value\" }, [\n            _vm._v(_vm._s(_vm.details.source)),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"details-item\" }, [\n          _c(\"div\", { staticClass: \"details-item-label\" }, [\n            _vm._v(\"发布时间\"),\n          ]),\n          _c(\"div\", { staticClass: \"details-item-value\" }, [\n            _vm._v(_vm._s(_vm.details.publishDate)),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"details-item\" }, [\n        _c(\"div\", { staticClass: \"details-item-label\" }, [_vm._v(\"附件\")]),\n        _c(\n          \"div\",\n          { staticClass: \"details-item-value\" },\n          [\n            _vm.details.attachmentList\n              ? _vm._l(\n                  _vm.details.attachmentList.filter(\n                    (data) => data.moduleType !== \"content\"\n                  ),\n                  function (item, index) {\n                    return _c(\n                      \"div\",\n                      { key: index, staticClass: \"details-item-file\" },\n                      [\n                        _vm._v(_vm._s(item.fileName) + \" \"),\n                        _c(\n                          \"span\",\n                          {\n                            on: {\n                              click: function ($event) {\n                                return _vm.FilePreview(item)\n                              },\n                            },\n                          },\n                          [_vm._v(\"预览\")]\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"span\",\n                          {\n                            on: {\n                              click: function ($event) {\n                                return _vm.fileClick(item)\n                              },\n                            },\n                          },\n                          [_vm._v(\"下载\")]\n                        ),\n                      ]\n                    )\n                  }\n                )\n              : _vm._e(),\n          ],\n          2\n        ),\n      ]),\n      _c(\"div\", {\n        staticClass: \"details-content\",\n        domProps: { innerHTML: _vm._s(_vm.details.content) },\n      }),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAT,GAAkB;EAC7B,IAAIC,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAyD,CAChEF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA0C,CAACH,GAAG,CAACI,EAAJ,CAAO,IAAP,CAAD,CAA1C,CAD8D,EAEhEH,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA6C,CAC7CF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAC/CF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAACH,GAAG,CAACI,EAAJ,CAAO,IAAP,CAAD,CAA/C,CAD6C,EAE/CH,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAC/CH,GAAG,CAACI,EAAJ,CAAOJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,OAAJ,CAAYC,KAAnB,CAAP,CAD+C,CAA/C,CAF6C,CAA/C,CAD2C,EAO7CN,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAgD,CAChDF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAyC,CACzCF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAC/CH,GAAG,CAACI,EAAJ,CAAO,MAAP,CAD+C,CAA/C,CADuC,EAIzCH,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAC/CH,GAAG,CAACI,EAAJ,CAAOJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,OAAJ,CAAYE,aAAnB,CAAP,CAD+C,CAA/C,CAJuC,CAAzC,CAD8C,EAShDP,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAyC,CACzCF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAACH,GAAG,CAACI,EAAJ,CAAO,KAAP,CAAD,CAA/C,CADuC,EAEzCH,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAC/CH,GAAG,CAACI,EAAJ,CAAOJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,OAAJ,CAAYG,QAAnB,CAAP,CAD+C,CAA/C,CAFuC,CAAzC,CAT8C,CAAhD,CAP2C,EAuB7CR,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAgD,CAChDF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAyC,CACzCF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAACH,GAAG,CAACI,EAAJ,CAAO,IAAP,CAAD,CAA/C,CADuC,EAEzCH,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAC/CH,GAAG,CAACI,EAAJ,CAAOJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,OAAJ,CAAYI,MAAnB,CAAP,CAD+C,CAA/C,CAFuC,CAAzC,CAD8C,EAOhDT,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAyC,CACzCF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAC/CH,GAAG,CAACI,EAAJ,CAAO,MAAP,CAD+C,CAA/C,CADuC,EAIzCH,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAC/CH,GAAG,CAACI,EAAJ,CAAOJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,OAAJ,CAAYK,WAAnB,CAAP,CAD+C,CAA/C,CAJuC,CAAzC,CAP8C,CAAhD,CAvB2C,EAuC7CV,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAyC,CACzCF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAACH,GAAG,CAACI,EAAJ,CAAO,IAAP,CAAD,CAA/C,CADuC,EAEzCH,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEH,GAAG,CAACM,OAAJ,CAAYM,cAAZ,GACIZ,GAAG,CAACa,EAAJ,CACEb,GAAG,CAACM,OAAJ,CAAYM,cAAZ,CAA2BE,MAA3B,CACGC,IAAD,IAAUA,IAAI,CAACC,UAAL,KAAoB,SADhC,CADF,EAIE,UAAUC,IAAV,EAAgBC,KAAhB,EAAuB;IACrB,OAAOjB,EAAE,CACP,KADO,EAEP;MAAEkB,GAAG,EAAED,KAAP;MAAcf,WAAW,EAAE;IAA3B,CAFO,EAGP,CACEH,GAAG,CAACI,EAAJ,CAAOJ,GAAG,CAACK,EAAJ,CAAOY,IAAI,CAACG,QAAZ,IAAwB,GAA/B,CADF,EAEEnB,EAAE,CACA,MADA,EAEA;MACEoB,EAAE,EAAE;QACFC,KAAK,EAAE,UAAUC,MAAV,EAAkB;UACvB,OAAOvB,GAAG,CAACwB,WAAJ,CAAgBP,IAAhB,CAAP;QACD;MAHC;IADN,CAFA,EASA,CAACjB,GAAG,CAACI,EAAJ,CAAO,IAAP,CAAD,CATA,CAFJ,EAaEJ,GAAG,CAACI,EAAJ,CAAO,GAAP,CAbF,EAcEH,EAAE,CACA,MADA,EAEA;MACEoB,EAAE,EAAE;QACFC,KAAK,EAAE,UAAUC,MAAV,EAAkB;UACvB,OAAOvB,GAAG,CAACyB,SAAJ,CAAcR,IAAd,CAAP;QACD;MAHC;IADN,CAFA,EASA,CAACjB,GAAG,CAACI,EAAJ,CAAO,IAAP,CAAD,CATA,CAdJ,CAHO,CAAT;EA8BD,CAnCH,CADJ,GAsCIJ,GAAG,CAAC0B,EAAJ,EAvCN,CAHA,EA4CA,CA5CA,CAFuC,CAAzC,CAvC2C,EAwF7CzB,EAAE,CAAC,KAAD,EAAQ;IACRE,WAAW,EAAE,iBADL;IAERwB,QAAQ,EAAE;MAAEC,SAAS,EAAE5B,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,OAAJ,CAAYuB,OAAnB;IAAb;EAFF,CAAR,CAxF2C,CAA7C,CAF8D,CAAzD,CAAT;AAgGD,CAnGD;;AAoGA,IAAIC,eAAe,GAAG,EAAtB;AACA/B,MAAM,CAACgC,aAAP,GAAuB,IAAvB;AAEA,SAAShC,MAAT,EAAiB+B,eAAjB"}]}