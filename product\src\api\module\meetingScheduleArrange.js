// 导入封装的方法
import {
  post
} from '../http'
// 模块管理
const meetingScheduleArrange = {
  // List (params) {
  //   return post('/moduleapp/list?', params)
  // },
  List (params) {
    return post('/meetingschedule/list', params)
  },
  Info (params) {
    return post(`/meetingschedule/info/${params}`)
  },
  Del (params) {
    return post('/meetingschedule/dels', params)
  }
}
export default {
  ...meetingScheduleArrange
}
