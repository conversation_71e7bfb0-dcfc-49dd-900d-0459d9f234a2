// 导入封装的方法
import { get, post, downloadWord, fileRequest, getFile, filedownload } from '../http'
import Vue from 'vue'
// 立法规划计划
const legislationProgramPlan = {
  // 立法规划列表查询接口
  programList (params) {
    return post('/program/list', params)
  },
  // 立法规划详情接口
  programInfo (params) {
    return post(`/program/info/${params}`)
  },
  // 立法规划删除接口
  programDel (params) {
    return post('/program/dels', params)
  },
  // 获取立法规划届次
  programType (params) {
    return post('/dictionary/programType', params)
  },
  // 立法计划查询列表
  planningList (params) {
    return post('/planning/list', params)
  },
  // 立法计划详情
  planningInfo (params) {
    return post(`/planning/info/${params}`)
  },
  // 立法计划删除接口
  planningDel (params) {
    return post('/planning/dels', params)
  },
  // 立法计划年度查询
  yearSelect (params) {
    return post('/historycircles/yearSelect', params)
  },
  // 项目征集列表查询
  projectcollectList (params) {
    return post('/projectcollect/list', params)
  },
  // 项目征集列表查询
  projectcollectSelectList (params) {
    return post('/projectcollect/selectList', params)
  },
  // 项目征集详情
  projectcollectInfo (params) {
    return post(`/projectcollect/info/${params}`)
  },
  // 项目征集列表删除
  projectcollectDel (params) {
    return post('/projectcollect/dels', params)
  },
  // 会议类型字典查询
  lawMeetingType (params) {
    return post('/dictionary/lawMeetingType', params)
  },
  // 会议列表查询
  meetingList (params) {
    return post('/meeting/list', params)
  },
  // 会议列表查询详情
  meetingInfo (params) {
    return post(`/meeting/info/${params}`)
  },
  // 会议列表查询删除
  meetingDel (params) {
    return post('/meeting/dels', params)
  },
  // 公开列表查询
  planningopenList (params) {
    return post('/planningopen/list', params)
  },
  // 公开列表查询详情
  planningopenInfo (params) {
    return post(`/planningopen/info/${params}`)
  },
  // 公开列表查询删除
  planningopenDel (params) {
    return post('/planningopen/dels', params)
  },
  // 项目管理页面列表查询
  lawprojectapproveList (params) {
    return post('/lawprojectapprove/list', params)
  },
  // 项目审批详情
  lawprojectapproveInfo (params) {
    return post(`/lawprojectapprove/info/${params}`)
  },
  // 项目管理页面列表删除
  lawprojectapproveDel (params) {
    return post('/lawprojectapprove/dels', params)
  },
  // 项目申报详情
  projectInfo (params) {
    return post(`/project/info/${params}`)
  },
  // 项目规划计划列表接口
  lawprojectapproveProjectList (params) {
    return post('/lawprojectapprove/projectList', params)
  },
  // 项目规划计划列表接口
  planninglifecycleList (params) {
    return post('/planninglifecycle/list', params)
  }
}
const opinionCollect = {
  opinioncollectletterDels (params) { // 意见征集函删除
    return post('/opinioncollectletter/dels', params)
  },
  opinioncollectletterInfo (params) { // 意见征集函详情
    return get(`/opinioncollectletter/info/${params}`)
  },
  opinionCollectList (params) { // 意见征集管理列表
    return get('/opinioncollect/list', params)
  },
  opinioncollectInfo (params) { // 意见征集详情
    return get(`/opinioncollect/info/${params}`)
  },
  opinioncollectAcceptList (params) { // 意见征集列表
    return get('/opinioncollect/accept/list', params)
  },
  opinioncollectDels (params) { // 意见征集管理删除
    return post('/opinioncollect/dels', params)
  },
  opinioncollectfeedbackList (params) { // 意见列表
    return get('/opinioncollectfeedback/list', params)
  },
  opinioncollectfeedbackAdd (params) { // 新增意见
    return post('/opinioncollectfeedback/add', params)
  },
  opinioncollectfeedbackInfo (params) { // 意见详情
    return get(`/opinioncollectfeedback/info/${params}`)
  },
  opinioncollectfeedbackDels (params) { // 意见删除
    return post('/opinioncollectfeedback/dels', params)
  },
  opinioncollectNationList (params) { // 关联国家立法列表
    return get('/opinioncollect/nation/list', params)
  },
  opinioncollectqrCodeShare (params) { // 意见征集/征集函分享
    return get('/opinioncollect/qrCodeShare', params)
  },
  opinioncollectInfoShare (params) { // 意见征集/征集函分享信息
    return get('/opinioncollect/infoShare', params)
  },
  opinioncollectAddShare (params) { // 新增意见[公众]
    return post('/opinioncollectfeedback/addShare', params)
  },
  opinioncollectDelsShare (params) { // 删除意见征集[公众]
    return post('/opinioncollect/delsShare', params)
  },
  opinioncollectAcceptInfo (params) { // 接收-单位征集函信息
    return get('/opinioncollect/accept/info', params)
  },
  opinioncollectAcceptLinkInfo (params) { // 接收-获取单位问卷链接信息
    return get('/opinioncollect/accept/linkInfo', params)
  },
  opinioncollectOpinionInfo (params) { // 意见征集--查询意见对应的附件内容
    return get('/opinioncollect/opinionInfo', params)
  },
  opinioncollectfeedbackOpinionInfo (params) { // 意见列表--查询意见对应的附件信息
    return get('/opinioncollectfeedback/opinionInfo', params)
  },
  opinioncollectfeedbackLinkList (params) { // 问卷对应的意见列表
    return get('/opinioncollectfeedback/link/list', params)
  },
  letterMouldList (params) { // 征集函模板列表
    return post('/opinioncollectmould/letterlist', params)
  },
  letterMouldInfo (params) { // 征集函内容
    return post(`/opinioncollectmould/info/${params}`)
  },
  opinionpersonlist (params) { // 意见发布人列表
    return get('/opinioncollectfeedback/personlist', params)
  },
  generalAdd (url, params) {
    return post(url, params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  opinioncollectfeedbackCount (params) { // 法规-----公众（数量统计）
    return post('/opinioncollectfeedback/count', params)
  }
}
const legislationProgress = {
  ruleList (params) { // 起草列表
    return post('/rule/list?', params)
  },
  ruleDel (params) { // 起草列表删除
    return post('/rule/dels?', params)
  },
  ruleInfo (params) { // 起草详情
    return post(`/rule/info/${params}`)
  },
  ruleverList (params) { // 草案相关列表
    return post('/rulever/list', params)
  },
  ruleverDels (params) { // 删除草案版本
    return post('/rulever/dels', params)
  },
  ruleverInfo (params) { // 草案版本详情
    return post(`/rulever/info/${params}`)
  },
  rulesubmitList (params) { // 报送列表
    return post('/rulesubmit/list', params)
  },
  rulesubmitAdd (params) { // 法规报送
    return post('/rulesubmit/add', params)
  },
  ruleverTypeList (params) { // 会议选草案
    return post('/rulever/type/list?', params)
  },
  ruleopenList (params) { // 获取法规公开列表
    return post('/ruleopen/list?', params)
  },
  ruleopenRuleList (params) { // 获取法规的公开记录
    return post('/ruleopen/rule/list?', params)
  },
  ruleverSwitchVer (params) { // 获取版本转换数据
    return post('/rulever/switchVer', params)
  },
  rulelifecycleList (params) { // 获取立法生命周期
    return post('/rulelifecycle/list?', params)
  },
  ruleverRename (params) { // 草案重命名
    return post('/rulever/rename', params)
  },
  ruleverIsShare (params) { // 草案共享/取消共享
    return post('/rulever/isShare', params)
  },
  downloadLawZipFile (params, text) { // 批量下载附件zip
    return fileRequest('/rulever/downloadZip', params, text)
  },
  ruleopenInfo (params) { // 获取立法生命周期
    return post(`/ruleopen/info/${params}`)
  },
  ruleverAdd (params) { // 添加草案版本
    return post('/rulever/add', params)
  },
  ruleverEdit (params) { // 编辑草案版本
    return post('/rulever/edit', params)
  },
  ruleCourseList (params) { // 编辑草案版本
    return post('/rule/courseList', params)
  }
}
const legislationWisdomAided = {
  wordshowOpenWordByUpload (params) { // word转html
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/openWordByUpload`, params)
  },
  wordshowSaveBack (params) { // html转word
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return filedownload(`${dsjUrl}/wordshow/saveBack`, params)
  },
  wordshowFormatChecks (params) { // 格式校验
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/formatChecks`, params)
  },
  wordshowTextChecks (params) { // 语义校验接口
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/textChecks`, params)
  },
  wordshowWordToHtml (params) { // 文件上传word转html
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/wordToHtml`, params)
  },
  wordshowPaintedWord (params) { // 生成花脸稿
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/paintedWord`, params)
  },
  wordshowContrastWord (params) { // 生成对照稿
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/contrastWord`, params)
  },
  wordshowChangeDescWord (params) { // 生成修改说明
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/changeDescWord`, params)
  },
  wordshowFormatReplace (params) { // 花脸稿转清稿
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/formatReplace`, params)
  },
  wordshowMergeWord1 (params) { // 汇报材料（合并）
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/mergeWord`, params)
  },
  wordshowSplitWord1 (params) { // 汇报材料（拆分）
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/splitWord`, params)
  },
  wordshowLawCompare (params) { // 法规比对
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/lawCompare`, params)
  },
  wordshowTemplateProduce1 (params) { // 模板生成
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return filedownload(`${dsjUrl}/wordshow/templateProduce`, params)
  }
}
const legislation = {
  ...legislationProgramPlan,
  ...opinionCollect,
  ...legislationProgress,
  ...legislationWisdomAided,
  ruleopenLawRuleList (params) { // 法规列表
    return post('/ruleopen/lawRule/list?', params)
  },
  ruleopenLawRuleDel (params) { // 法规列表
    return post('/ruleopen/lawRule/dels', params)
  },
  ruleopenLawRuleInfo (params) { // 法规列表
    return post('/ruleopen/lawRule/info', params)
    // return post(`/ruleopen/lawRule/info/${params}`)
  },
  lawSearchList (params) { // 法规列表
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/fullsearch/law/list`, params)
  },
  dataDetail (params) { // 法规详情
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/fullsearch/info`, params)
  },
  dataRecomment (params) { // 法规推荐
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/recommendData`, params)
  },
  specialTopic (params) { // 法规专题
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/specialTopic`, params)
  },
  validityRanks (params) { // 法规效力级别
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/validityRank`, params)
  },
  validityRanksTips (params) { // 法规效力级别
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/regulations/validityRank`, params)
  },
  hotSearch (params) { // 法规热门搜索
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/fullsearch/hotsearch`, params)
  },
  hotReads (params) { // 法规热门阅读
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/hotread`, params)
  },
  regulationsList (params) { // 法规条例搜索
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/regulations/list`, params)
  },
  getDyArea (params) { // 立法动态地区
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/dyLegislation/getDyArea`, params)
  },
  dyLegislationList (params) { // 立法动态列表
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/dyLegislation/list`, params)
  },
  detailInfo (params) { // 立法动态详情
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/dyLegislation/info`, params)
  },
  recommendData (params) { // 立法动态推荐
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/dyLegislation/recommendData`, params)
  },
  attachmentReview (params) { // 技术材料预览
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return getFile(`${dsjUrl}/attachment/toStream`, params)
  },
  fileDownload (params) { // 技术材料下载
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return getFile(`${dsjUrl}/attachment/downloadFile`, params)
  },
  attachmentUploadFile (params) { // 技术材料上传
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/attachment/uploadFile`, params)
  },
  attachmentDownload (params, texts, types) { // 技术材料下载
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return downloadWord(`${dsjUrl}/attachment/downloadFile`, params, texts, types)
  },
  attachmentFileList (params) { // 技术材料列表
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/attachment/fileList`, params)
  },
  attachmentDeleteFile (params) { // 技术材料删除
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/attachment/deleteFile`, params)
  },
  attachmentDownloadZip (params, texts, types) { // 技术材料下载zip
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return downloadWord(`${dsjUrl}/attachment/downloadZip`, params, texts, types)
  },
  wordToHtml (params) { // 文件上传word转html
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/wordToHtml`, params)
  },
  paintedWord (params) { // 生成花脸稿接口
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/paintedWord`, params)
  },
  saveBack (params, texts, types) { // 文件回存并下载
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return downloadWord(`${dsjUrl}/wordshow/saveBack`, params, texts, types)
  },
  saveBack1 (params, texts, types) { // 文件回存并下载
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return filedownload(`${dsjUrl}/wordshow/saveBack`, params, texts, types)
  },
  lawCompare (params) { // 法规比对
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/lawCompare`, params)
  },
  textChecks (params) { // 语义校验接口
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/textChecks`, params)
  },
  templateLoad (params) { // 模板加载
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/templateLoad`, params)
  },
  openNewWord (params) { // 打开空白word接口
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/openNewWord`, params)
  },
  formatChecks (params) { // 格式校验
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/formatChecks`, params)
  },
  openWordByUpload (params) { // 在线编辑word接口
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/openWordByUpload`, params)
  },
  contrastWord (params) { // 生成对照稿
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/contrastWord`, params)
  },
  changeDescWord (params) { // 生成修改说明
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/changeDescWord`, params)
  },
  formatReplace (params) { // 花脸稿转清稿
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/formatReplace`, params)
  },
  wordshowSave (params) { // word保存
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/save`, params)
  },
  wordshowOpenEditWord (params) { // 打开编辑窗口（立法相关）
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/openEditWord`, params)
  },
  wordshowMergeWord (params) { // 汇报材料（合并）
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/mergeWord`, params)
  },
  wordshowSplitWord (params) { // 汇报材料（拆分）
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/splitWord`, params)
  },
  wordshowDownloadZip (params, texts, types) { // 拆分材料下载zip
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return downloadWord(`${dsjUrl}/wordshow/downloadZip`, params, texts, types)
  },
  wordshowTemplates (params) { // 模板列表
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/wordshow/templates`, params)
  },
  wordshowGetFields (params) { // 格式生成
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/getFields`, params)
  },
  wordshowTemplateProduce (params) { // 模板生成
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/wordshow/templateProduce`, params)
  },
  analyseRuleopenCount () { // 驾驶舱---立法统计
    return post('law/analyse/ruleopenCount')
  },
  analyseNewRuleopenCount () { // 驾驶舱---立法统计[新]
    return post('law/analyse/new/ruleopenCount')
  },
  analyseRuleopen5Year () { // 驾驶舱---5年立法统计【新】
    return post('law/analyse/ruleopen5Year')
  },
  analyseTopics () { // 驾驶舱---法规分类
    return post('law/analyse/topics')
  },
  analyseNewTopics () { // 驾驶舱---法规分类[新]
    return post('law/analyse/new/topics')
  },
  analyseTags () { // 驾驶舱---法规标签
    return post('/law/analyse/tags')
  },
  analyseSubmitUnit () { // 驾驶舱---提请单位
    return post('/law/analyse/submitUnit')
  },
  analyseFirstCommittee () { // 驾驶舱---初审委员会单位
    return post('/law/analyse/firstCommittee')
  },
  analyseNewOpinion () { // 驾驶舱---意见征集统计【新】
    return post('/law/analyse/new/opinion')
  },
  analyseNewOpinionFeedback () { // 驾驶舱---意见统计【新】
    return post('/law/analyse/new/opinionFeedback')
  },
  analyseOpinion () { // 驾驶舱---意见征集统计
    return post('/law/analyse/opinion')
  },
  analyseOpinionTrend () { // 驾驶舱---意见征集走势
    return post('/law/analyse/opinionTrend')
  },
  analyseNewOpinionTrend () { // 驾驶舱---意见征集走势
    return post('/law/analyse/new/opinionTrend')
  },
  analyseOpinionTop (data) { // 驾驶舱---法规意见、支持、反对top5
    return get(`/law/analyse/opinionTop?type=${data}`)
  },
  analyseNewOpinionTop (data) { // 驾驶舱---法规意见、支持、反对top10 【新】
    return get(`/law/analyse/new/opinionTop?type=${data}`)
  },
  analyseDraftUnit () { // 起草单位
    return get('/law/analyse/draftUnit')
  },
  analyseNewDraftUnit () { // 起草单位【新】
    return get('/law/analyse/new/draftUnit')
  },
  ruleInterveneApplyList (params) { // 查询介入申请列表
    return get('/ruleInterveneApply/list', params)
  },
  ruleInterveneApplyInfo (params) { // 获取介入申请详情
    return get(`/ruleInterveneApply/info/${params}`)
  },
  ruleInterveneApplyDels (params) { // 删除介入申请
    return post('ruleInterveneApply/dels', params)
  },
  ruleReportApproveList (params) { // 查询法规报批申请列表
    return get('/ruleReportApprove/list', params)
  },
  ruleReportApproveInfo (params) { // 获取法规报批申请详情
    return get(`/ruleReportApprove/info/${params}`)
  },
  ruleReportApproveDels (params) { // 删除法规报批申请
    return post('ruleReportApprove/dels', params)
  },
  ruleReportApproveFlowList (params) { // 节点流程报批查询
    return get('/ruleReportApprove/flow/list', params)
  },
  ruleInterveneFeedbackList (params) { // 查询介入申请反馈列表
    return get('/ruleInterveneFeedback/list', params)
  },
  ruleReportApproveFeedbackInfo (params) { // 获取报批反馈详情
    return get(`/ruleReportApproveFeedback/info/${params}`)
  },
  ruleReportApproveFeedbackList (params) { // 查询报批反馈列表
    return get('/ruleReportApproveFeedback/list', params)
  },
  ruleInterveneFeedbackInfo (params) { // 获取介入申请反馈详情
    return get(`/ruleInterveneFeedback/info/${params}`)
  },
  areaOpenCities (params) { // 地区
    return post('/area/open/cities', params)
  },
  ruleReportApproveProvinceList (params) { // 市法规批准查询列表
    return get('/ruleReportApprove/province/list', params)
  },
  lawNormalFileList (params) { // 法律法规+抓取的规范性文件搜索接口
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/law/normalfile/list`, params)
  },
  lawNormalFileValidityRank (params) { // 法律+抓取的规范性文件效力级别统计
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/law/normalfile/validityRank`, params)
  },
  fullsearchSpecialTopic (params) { // 法律+抓取的标签
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/specialTopic`, params)
  },
  lawNormalFileNormalfileGroup (params) { // 法律法规+抓取的规范性文件左侧统计【新】
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/law/normalfile/group`, params)
  },
  regulationsGroup (params) { // 条例左侧统计【新】
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/regulations/group`, params)
  },
  regulationsNormalfileList (params) { // 法律法规条例搜索【新】
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/regulations/normalfile/list`, params)
  },
  rightRegulationsValidityRank (params) { // 右侧---条例统计
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/right/regulations/validityRank`, params)
  },
  rightValidityRank (params) { // 右侧---法律法规+抓取的规范性文件效力级别统计
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/right/validityRank`, params)
  },
  fullsearchRightList (params) { // 右侧---法律法规+抓取规范性文件列表
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/right/list`, params)
  },
  fullsearchRegulationsList (params) { // 右侧---条例搜索
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/fullsearch/regulations/list`, params)
  },
  getDataDetail (params) { // 法规详情
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/fullsearch/getInfo`, params)
  },
  normalFileRecommendData (params) { // 资料推荐接口(新)
    const dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/fullsearch/normalfile/recommendData`, params)
  },
  ruleClearList (params) { // 查询法规清理列表
    return get('/ruleClear/list', params)
  },
  ruleClearInfo (params) { // 查询法规清理详情
    return get(`/ruleClear/info/${params}`)
  },
  ruleClearDel (params) { // 删除法规清理
    return post('/ruleClear/dels', params)
  },
  ruleClearProjectList (params) { // 获取法规清理项目列表
    return get('/ruleClearProject/list', params)
  },
  ruleClearProjectInfo (params) { // 获取法规清理项目详情
    return get(`/ruleClearProject/info/${params}`)
  },
  ruleClearProjectDel (params) { // 删除法规清理项目
    return post('/ruleClearProject/dels', params)
  },
  ruleClearProjectTypes (params) { // 法规清理左侧类型列表
    return get('/ruleClearProject/types', params)
  },
  lawExpertList (params) { // 查询列表立法专家接口
    return get('/lawExpert/list?', params)
  },
  lawExpertAdd (params) { // 新增立法专家接口
    return post('/lawExpert/add?', params)
  },
  lawExpertInfo (params) { // 立法专家详情接口
    return get(`/lawExpert/info/${params}`)
  },
  lawExpertWorkout (params) { // 解聘立法专家接口
    return post('/lawExpert/workout', params)
  },
  lawExpertDeletes (params) { // 删除立法专家接口
    return post('/lawExpert/dels?', params)
  },
  opinionCollectCount (params) { // 意见统计
    return post('/opinionCollect/count?', params)
  },
  ruleAssessList (params) { // 查询立法后评估列表接口
    return get('/ruleAssess/list?', params)
  },
  ruleAssessDels (params) { // 删除立法后评估接口
    return post('/ruleAssess/dels?', params)
  },
  ruleAssessInfo (params) { // 查询立法后评估接口
    return get(`/ruleAssess/info/${params}`)
  },
  ruleAssessProjectTypes (params) { // 法规评估事项左侧类型列表
    return get('/ruleAssessProject/types', params)
  },
  ruleAssessProjectList (params) { // 法规评估事项查询列表
    return get('/ruleAssessProject/list', params)
  },
  ruleAssessHandleList (params) { // 查询立法评论办结列表
    return get('/ruleAssessHandle/list', params)
  },
  ruleAssessHandleAdd (params) { // 新增立法评论办结
    return post('/ruleAssessHandle/add', params)
  },
  ruleAssessHandleEdit (params) { // 修改立法评论办结
    return post('/ruleAssessHandle/edit', params)
  },
  ruleAssessHandleInfo (params) { // 查询立法评论办结详情
    return get(`/ruleAssessHandle/info/${params}`)
  },
  ruleAssessProjectAdd (params) { // 新增立法评论事项
    return post('/ruleAssessProject/add', params)
  },
  ruleAssessProjectEdit (params) { // 编辑立法评论事项
    return post('/ruleAssessProject/edit', params)
  },
  ruleAssessProjectDels (params) { // 批量删除法规评估事项
    return post('/ruleAssessProject/dels?', params)
  },
  ruleAssessProjectInfo (params) { // 查询立法评论事项
    return get(`/ruleAssessProject/info/${params}`)
  },
  generalGetList (url, params) {
    return get(url, params)
  },
  generalGetInfo (url, params) {
    return get(`${url}/${params}`)
  },
  documentCollectHasCollectIds () { // 查询已收藏的文档id
    return get('/documentCollect/hasCollectIds')
  },
  documentCollectAdd (params) { //  新增收藏的文档
    return post('/documentCollect/add', params)
  },
  documentCollectDels (params) { // 删除收藏的文档
    return post('/documentCollect/dels?', params)
  },
  documentCollectEdit (params) { // 修改收藏的文档
    return post('/documentCollect/edit', params)
  },
  documentCollectInfo (params) { // 删除收藏的文档
    return get(`/documentCollect/info/${params}`)
  },
  documentCollectList (params) { // 查询收藏的文档列表
    return get('/documentCollect/list?', params)
  },
  documentFolderMyFolder () { // 查询我的收藏文件夹树
    return get('/documentFolder/myFolder')
  },
  documentFolderDels (params) { // 删除我的收藏文件夹
    return post('/documentFolder/dels?', params)
  },
  documentFolderEdit (params) { // 修改我的收藏文件夹
    return post('/documentFolder/edit', params)
  },
  documentFolderAdd (params) { // 新增我的收藏文件夹
    return post('/documentFolder/add', params)
  }

}
export default legislation
