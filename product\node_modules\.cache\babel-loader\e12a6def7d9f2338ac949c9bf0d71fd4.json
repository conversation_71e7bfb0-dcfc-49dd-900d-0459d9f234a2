{"remainingRequest": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\jnzx\\product\\src\\views\\app-management\\committee-data\\components\\committeeDataDetails.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\jnzx\\product\\src\\views\\app-management\\committee-data\\components\\committeeDataDetails.vue", "mtime": 1762767300301}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdjb21taXR0ZWVEYXRhRGV0YWlscycsCgogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkZXRhaWxzOiB7fQogICAgfTsKICB9LAoKICBwcm9wczogWydpZCddLAoKICBtb3VudGVkKCkgewogICAgdGhpcy5pbmZvcm1hdGlvbkxpc3RJbmZvKCk7CiAgfSwKCiAgbWV0aG9kczogewogICAgYXN5bmMgaW5mb3JtYXRpb25MaXN0SW5mbygpIHsKICAgICAgY29uc3QgcmVzID0gYXdhaXQgdGhpcy4kYXBpLmFwcE1hbmFnZW1lbnQuaW5mb3JtYXRpb25MaXN0SW5mbyh0aGlzLmlkKTsKICAgICAgdmFyIHsKICAgICAgICBkYXRhCiAgICAgIH0gPSByZXM7CiAgICAgIHRoaXMuZGV0YWlscyA9IGRhdGE7CiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZGV0YWlscy5hdHRhY2hMaXN0KTsKICAgIH0sCgogICAgRmlsZVByZXZpZXcocm93KSB7CiAgICAgIGNvbnN0IHJvdXRlRGF0YSA9IHRoaXMuJHJvdXRlci5yZXNvbHZlKHsKICAgICAgICBwYXRoOiAnL0ZpbGVQcmV2aWV3JywKICAgICAgICBxdWVyeTogewogICAgICAgICAgaWQ6IHJvdy5pZCwKICAgICAgICAgIGZpbGVVcmw6IHJvdy5maWxlUGF0aCwKICAgICAgICAgIGZpbGVOYW1lOiByb3cuZmlsZU5hbWUsCiAgICAgICAgICBmaWxlVHlwZTogcm93LmZpbGVUeXBlLAogICAgICAgICAgcm91dGU6ICcvRmlsZVByZXZpZXcnCiAgICAgICAgfQogICAgICB9KTsKICAgICAgd2luZG93Lm9wZW4ocm91dGVEYXRhLmhyZWYsICdfYmxhbmsnKTsKICAgIH0sCgogICAgZmlsZUNsaWNrKHJvdykgewogICAgICB0aGlzLiRhcGkucHJvcG9zYWwuZG93bmxvYWRGaWxlKHsKICAgICAgICBpZDogcm93LmlkCiAgICAgIH0sIHJvdy5maWxlTmFtZSk7CiAgICB9CgogIH0KfTs="}, {"version": 3, "mappings": "AA4CA;EACAA,4BADA;;EAEAC;IACA;MACAC;IADA;EAGA,CANA;;EAOAC,aAPA;;EAQAC;IACA;EACA,CAVA;;EAWAC;IACA;MACA;MACA;QAAAJ;MAAA;MACA;MACAK;IACA,CANA;;IAOAC;MACA;QAAAC;QAAAC;UAAAC;UAAAC;UAAAC;UAAAC;UAAAC;QAAA;MAAA;MACAC;IACA,CAVA;;IAWAC;MACA;QAAAN;MAAA;IACA;;EAbA;AAXA", "names": ["name", "data", "details", "props", "mounted", "methods", "console", "FilePreview", "path", "query", "id", "fileUrl", "fileName", "fileType", "route", "window", "fileClick"], "sourceRoot": "src/views/app-management/committee-data/components", "sources": ["committeeDataDetails.vue"], "sourcesContent": ["<template>\r\n  <div class=\"committeeDataDetails details\">\r\n    <div class=\"details-title\">详情</div>\r\n    <div class=\"details-item-box\">\r\n      <div class=\"details-item-title\">\r\n        <div class=\"details-item-label\">标题</div>\r\n        <div class=\"details-item-value\">{{ details.title }}</div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">所属栏目</div>\r\n          <div class=\"details-item-value\">{{ details.structureName }}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">发布人</div>\r\n          <div class=\"details-item-value\">{{ details.createBy }}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">来源</div>\r\n          <div class=\"details-item-value\">{{ details.source }}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">发布时间</div>\r\n          <div class=\"details-item-value\">{{ details.publishDate }}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">附件</div>\r\n        <div class=\"details-item-value\">\r\n          <template v-if=\"details.attachmentList\">\r\n            <div class=\"details-item-file\"\r\n              v-for=\"(item, index) in details.attachmentList.filter(data => data.moduleType !== 'content')\"\r\n              :key=\"index\">{{ item.fileName }} <span @click=\"FilePreview(item)\">预览</span> <span\r\n                @click=\"fileClick(item)\">下载</span></div>\r\n          </template>\r\n        </div>\r\n      </div>\r\n      <div class=\"details-content\" v-html=\"details.content\"></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'committeeDataDetails',\r\n  data () {\r\n    return {\r\n      details: {}\r\n    }\r\n  },\r\n  props: ['id'],\r\n  mounted () {\r\n    this.informationListInfo()\r\n  },\r\n  methods: {\r\n    async informationListInfo () {\r\n      const res = await this.$api.appManagement.informationListInfo(this.id)\r\n      var { data } = res\r\n      this.details = data\r\n      console.log(this.details.attachList)\r\n    },\r\n    FilePreview (row) {\r\n      const routeData = this.$router.resolve({ path: '/FilePreview', query: { id: row.id, fileUrl: row.filePath, fileName: row.fileName, fileType: row.fileType, route: '/FilePreview' } })\r\n      window.open(routeData.href, '_blank')\r\n    },\r\n    fileClick (row) {\r\n      this.$api.proposal.downloadFile({ id: row.id }, row.fileName)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.committeeDataDetails {\r\n  height: 100%;\r\n  padding: 24px;\r\n\r\n  .details-content {\r\n    width: 100%;\r\n    padding: 40px;\r\n\r\n    img {\r\n      max-width: 700px;\r\n      margin: auto;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}