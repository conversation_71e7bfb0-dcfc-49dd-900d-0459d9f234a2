// 导入封装的方法
import Vue from 'vue'
import {
  post,
  get
} from '../http'

const workstationNew = {
  workstationWebURL () { // 工作站web端
    return JSON.parse(sessionStorage.getItem('workstationUrl' + Vue.prototype.$logo()))
  },
  workstationSupervise: { // 工作站管理
    manageList(params) { // 列表
      return get('work/station/manage/list', params)
    },
    manageInfo(params) { // 详情
      return post(`work/station/manage/info/${params}`)
    },
    manageAdd(params) { // 新增
      return post('work/station/manage/add', params)
    },
    manageEdit(params) { // 编辑
      return post('work/station/manage/edit', params)
    },
    manageDels(params) { // 批量删除
      return post('work/station/manage/dels', params)
    },
    manageUpdateIsUsing(params) { // 是否启用
      return post('work/station/manage/updateIsUsing', params)
    }
  },
  workstationRepresentative: { // 代表管理
    memberList(params) { // 列表
      return get('work/station/member/list', params)
    },
    queryList(params) { // 姓名搜索列表
      return get('work/station/member/queryList', params)
    },
    memberInfo(params) { // 详情
      return post(`work/station/member/info/${params}`)
    },
    memberInfoForEdit(params) { // 编辑
      return post(`work/station/member/infoForEdit/${params}`)
    },
    memberAdd(params) { // 新增
      return post('work/station/member/add', params)
    },
    memberEdit(params) { // 编辑
      return post('work/station/member/edit', params)
    },
    memberDels(params) { // 批量删除
      return post('work/station/member/dels', params)
    },
    memberUpdateIsUsing(params) { // 是否启用、禁用
      return post('work/station/member/updateIsUsing', params)
    },
    memberResetPWD(params) { // 重置密码
      return post('work/station/member/resetPWD', params)
    }
  },
  workstationRepresentativeAdd (url, params) {
    return post(url, params)
  },
  workstationRelease: { // 信息发布管理
    issueList(params) { // 列表
      return get('information/issue/list', params)
    },
    issueInfo(params) { // 详情
      return post(`information/issue/info/${params}`)
    },
    issueAdd(params) { // 新增
      return post('information/issue/add', params)
    },
    issueEdit(params) { // 编辑
      return post('information/issue/edit', params)
    },
    issueDels(params) { // 批量删除
      return post('information/issue/dels', params)
    },
    issueUpdateCheckStatus(params) { // 审核状态
      return post('information/issue/updateCheckStatus', params)
    }
  },
  workstationSubmitted: { // 信息报送
    reportList(params) { // 列表
      return get('information/report/list', params)
    },
    reportInfo(params) { // 详情
      return post(`information/report/info/${params}`)
    },
    reportAdd(params) { // 新增
      return post('information/report/add', params)
    },
    reportEdit(params) { // 编辑
      return post('information/report/edit', params)
    },
    reportDels(params) { // 批量删除
      return post('information/report/dels', params)
    }
  },
  workstationTheme: { // 主题图管理
    mapsList(params) { // 列表
      return get('topic/maps/list', params)
    },
    mapsInfo(params) { // 详情
      return post(`topic/maps/info/${params}`)
    },
    mapseAdd(params) { // 新增
      return post('topic/maps/add', params)
    },
    mapsEdit(params) { // 编辑
      return post('topic/maps/edit', params)
    },
    mapsDels(params) { // 批量删除
      return post('topic/maps/dels', params)
    }
  },
  workstationCivilian: { // 群众意见管理
    opinionList(params) { // 列表
      return get('public/opinion/list', params)
    },
    opinionInfo(params) { // 详情
      return post(`public/opinion/info/${params}`)
    },
    opinionAdd(params) { // 新增
      return post('public/opinion/add', params)
    },
    opinionEdit(params) { // 编辑
      return post('public/opinion/edit', params)
    },
    opinionDels(params) { // 批量删除
      return post('public/opinion/dels', params)
    },
    handleList(params) { // 群众意见处理列表
      return get('public/opinion/handle/list', params)
    },
    handleInfo(params) { // 群众意见处理详情
      return post(`public/opinion/handle/info/${params}`)
    },
    handleAdd(params) { // 群众意见处理新增
      return post('public/opinion/handle/add', params)
    },
    handleEdit(params) { // 群众意见处理编辑
      return post('public/opinion/handle/edit', params)
    },
    handleDels(params) { // 群众意见处理批量删除
      return post('public/opinion/handle/dels', params)
    },
    handleSmsSend(params) { // 提醒
      return post('public/opinion/handle/smsSend', params)
    },
    opinionChooseWorkStationOrMember(params) { // 选择工作站或者工作站代表
      return post('public/opinion/chooseWorkStationOrMember', params)
    },
    updateCheckStatus (params) { // 群众意见审核
      return get('public/opinion/updateCheckStatus', params)
    }
  },
  workstationPersonnel: { // 工作站工作人员管理
    wholeuserList(params) { // 列表
      return get('/work/station/manage/user/list', params)
    },
    wholeuserInfo(params) { // 详情
      return post(`wholeuser/info/${params}`)
    },
    wholeuserBatchDel(params) { // 删除
      return post('wholeuser/batch/del', params)
    },
    batchStartuse(params) { // 启用
      return post('wholeuser/batch/startuse', params)
    },
    batchStopuse(params) { // 禁用
      return post('wholeuser/batch/stopuse', params)
    }
  },
  workstationActivities: { // 活动管理
    workstationactivityList(params) { // 列表
      return get('workstationactivity/list', params)
    },
    workstationactivityInfo(params) { // 详情
      return post(`workstationactivity/info/${params}`)
    },
    workstationactivityAdd(params) { // 新增
      return post('workstationactivity/add', params)
    },
    workstationactivityEdit(params) { // 编辑
      return post('workstationactivity/edit', params)
    },
    workstationactivityDels(params) { // 删除
      return post('workstationactivity/dels', params)
    }
  },
  workstationStatistics: { // 工作站统计
    manageIndex(params) { // 列表
      return get('work/station/manage/index', params)
    },
    memberAdminList(params) { // 列表
      return get('work/station/member/admin/list', params)
    },
    manageCountStationAndMember(params) { // 数据统计列表
      return get('work/station/manage/countStationAndMember', params)
    }
  },
  workstationIndex: { // 工作站首页
    managemyWorkStation(params) { // 我的工作站列表
      return get('work/station/manage/myWorkStation', params)
    },
    manageHomePage(params) { // 我的工作站列表
      return get('work/station/manage/home/<USER>', params)
    },
    workstationactivityWebList(params) { // 活动列表
      return get('workstationactivity/admin/list', params)
    },
    issueWebList(params) { // 信息发布列表
      return get('information/issue/admin/list', params)
    },
    memberWebList(params) { // 委员列表
      return get('work/station/member/admin/list', params)
    }
  },
  chooseMyWorkStations (params) { // 当前工作站筛选列表
    return post('work/station/manage/chooseMyWorkStations?', params)
  },
  countStationData (params) { // 统计具体站点提交的信息总数
    return get('work/station/manage/countStationData', params)
  }
}
export default workstationNew
