// 导入封装的方法
import {
  post
} from '../http'
const reMaintenance = {
  // 所有地区获取
  areaList (params) {
    return post('/area/setList', params)
  },
  // 地区数量统计
  areaStatics (params) {
    return post('/area/areaStatics', params)
  },
  // 地区初始化
  areaInit (params) {
    return post('/area/areaInit', params)
  },
  // 地区初始化
  setListMaintain (params) {
    return post('/area/setListMaintain', params)
  }
}
export default reMaintenance
