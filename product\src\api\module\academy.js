// 导入封装的方法
import { post, fileRequest } from '../http'

const academy = {
  // 分类列表
  getSyTypeTree (params) {
    return post('/syType/getSyTypeTree?', params)
  },
  // 分类详情接口
  syTypeInfo (params) {
    return post('/syType/info?', params)
  },
  // 分类删除接口
  syTypeDels (params) {
    return post('/syType/dels?', params)
  },
  // 书库管理列表
  syBookList (params) {
    return post('/syBook/list?', params)
  },
  // 书库管理详情
  syBookInfo (params) {
    return post('/syBook/info', params)
  },
  // 书库管理删除
  syBookdels (params) {
    return post('/syBook/dels', params)
  },
  // 读书公告列表
  syNoticeList (params) {
    return post('/syNotice/list', params)
  },
  // 读书公告详情
  syNoticeInfo (params) {
    return post('/syNotice/info', params)
  },
  // 读书公告删除
  syNoticeDel (params) {
    return post('/syNotice/dels', params)
  },
  // 读书公告发布和撤回
  publish (params) {
    return post('/syNotice/publish?', params)
  },
  // 读书公告关联书籍接口
  syNoticeJoin (params) {
    return post('/syNotice/join/book', params)
  },
  // 读书公告关联活动接口
  syNoticeJoinScheme (params) {
    return post('/syNotice/join/scheme', params)
  },
  // 推荐阅读列表
  sySuggestedReadingList (params) {
    return post('/sySuggestedReading/list', params)
  },
  // 推荐阅读发布
  sySuggestedReadingIssue (params) {
    return post('/sySuggestedReading/issue', params)
  },
  // 推荐阅读取消发布
  sySuggestedReadingUnpublish (params) {
    return post('/sySuggestedReading/unpublish', params)
  },
  // 推荐阅读删除
  sySuggestedReadingDels (params) {
    return post('/sySuggestedReading/dels', params)
  },
  // 推荐阅读详情
  sySuggestedReadingInfo (params) {
    return post('/sySuggestedReading/info', params)
  },
  // 推荐阅读关联
  relevanceBook (params) {
    return post('/sySuggestedReading/relevanceBook', params)
  },
  // 推荐阅读修改显示类型
  changecount (params) {
    return post('/sySuggestedReading/changecount', params)
  },
  // 推荐阅读删除关联书籍
  removebook (params) {
    return post('/sySuggestedReading/removebook', params)
  },
  // 推荐阅读设置推荐词接口
  addWord (params) {
    return post('/sySuggestedReading/addWord', params)
  },
  // 精彩读书笔记登选列表
  syReadingNotesList (params) {
    return post('/syReadingNotes/list', params)
  },
  // 精彩读书笔记登选详情
  syReadingNotesInfo (params) {
    return post('/syReadingNotes/info', params)
  },
  // 精彩读书笔记登选删除
  syReadingNotesDel (params) {
    return post('/syReadingNotes/dels', params)
  },
  // 精彩读书笔记登选选登
  syReadingNotesDigest (params) {
    return post('/syReadingNotes/delsDigest', params)
  },
  // 精彩读书笔记登选撤下
  syReadingNotesUnDigest (params) {
    return post('/syReadingNotes/delsUnDigest', params)
  },
  // 书籍金玉良言选登列表
  goldwordList (params) {
    return post('/goldword/list?', params)
  },
  // 书籍金玉良言选登删除
  goldwordDel (params) {
    return post('/goldword/dels', params)
  },
  // 书籍金玉良言详情
  goldworInfo (params) {
    return post('/goldword/info', params)
  },
  // 书籍金玉良言登选选登
  goldwordDigest (params) {
    return post('/goldword/digest', params)
  },
  // 书籍金玉良言登选撤下
  goldwordUnDigest (params) {
    return post('/goldword/unDigest', params)
  },
  // 个人书架列表
  syMyBookList (params) {
    return post('/syMyBook/list', params)
  },
  // 【个人分类】列表
  syMyTypeList (params) {
    return post('/syMyType/list', params)
  },
  // 滚动推荐列表
  syRollBookList (params) {
    return post('/syRollBook/list', params)
  },
  // 滚动推荐详情
  syRollBookInfo (params) {
    return post('/syRollBook/info', params)
  },
  // 滚动推荐发布
  syRollBookIssue (params) {
    return post('/syRollBook/issue', params)
  },
  // 滚动推荐取消发布
  syRollBookUnpublish (params) {
    return post('/syRollBook/unpublish', params)
  },
  // 滚动推荐删除
  syRollBookDels (params) {
    return post('/syRollBook/dels', params)
  },
  // 我的读书笔记公开
  delsOpen (params) {
    return post('/syReadingNotes/delsOpen', params)
  },
  // 我的读书笔记私密
  delsPrivacy (params) {
    return post('/syReadingNotes/delsPrivacy', params)
  },
  // 交流群关联书籍
  talkgroupjoin (params) {
    return post('/syBook/talkgroup/join', params)
  },
  // 读书活动列表
  readschemeList (params) {
    return post('/readscheme/list', params)
  },
  // 读书活动详情
  readschemeInfo (params) {
    return post(`/readscheme/info/${params}`)
  },
  // 读书活动删除
  readschemeDel (params) {
    return post('/readscheme/dels', params)
  },
  // 读书活动关联书籍
  readschemeJoin (params) {
    return post('/readscheme/join/book', params)
  },
  // 读书活动发布
  readschemePublish (params) {
    return post('/readscheme/publish', params)
  },
  // 热门书籍统计
  librarycountBook (params) {
    return post('/librarycount/book?', params)
  },
  // 人员阅读统计
  librarycountUser (params) {
    return post('/librarycount/user?', params)
  },
  // 交流群活跃度统计
  librarycountGorup (params) {
    return post('/librarycount/gorup?', params)
  },
  // 混合排序列表
  recommendsortList (params) {
    return post('/recommendsort/list', params)
  },
  // 刷新混合排序列表
  recommendsortFlush (params) {
    return post('/recommendsort/flush', params)
  },
  // 总书库列表
  bookrepositoryList (params) {
    return post('/bookrepository/list?', params)
  },
  // 总库分类树
  bookrepositoryTypetree (params) {
    return post('/bookrepository/typetree', params)
  },
  // 总书库拉入书籍到单位书库
  bookrepositoryOutbound (params) {
    return post('/bookrepository/outbound?', params)
  },
  // 拉取掌阅书籍
  handreadPullBooks (params) {
    return post('/handread/pull/books', params)
  },
  // 群消息收藏列表
  groupmessageList (params) {
    return post('/groupmessage/list?', params)
  },
  // 群消息收藏列表删除
  groupmessageDel (params) {
    return post('/groupmessage/dels?', params)
  },
  // 群消息收藏列表详情
  groupmessageInfo (params) {
    return post(`/groupmessage/info/${params}`)
  },
  // 群消息收藏列表导出
  groupmessageExport (params) {
    fileRequest('/groupmessage/export', params, '消息导出.xlsx')
  },
  // 交流群消息列表
  groupMessage (params) {
    return post('/librarycount/groupMessage?', params)
  },
  // 交流群消息列表
  groupJoiner (params) {
    return post('/librarycount/groupJoiner?', params)
  }
}
export default academy
