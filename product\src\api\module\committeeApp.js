// 导入封装的方法
import {
  post
} from '../http'
// 模块管理
const committeeApp = {
  // newH5moduleList (params) {
  //   return post('/moduleapp/list?', params)
  // },
  meetingmoduleList (params) {
    return post('/meetingmodule/list', params)
  },
  meetingmoduleInfo (params) {
    return post(`/meetingmodule/info/${params}`)
  },
  meetingmoduleDel (params) {
    return post('/meetingmodule/dels', params)
  },
  meetingmoduleParentList (params) {
    return post('/meetingmodule/parentList', params)
  },
  moduleappkeySelect (params) {
    return post('/meetingmodule/select/list', params)
  }
}
export default {
  ...committeeApp
}
