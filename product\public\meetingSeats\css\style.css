:root {
  --color: #1A77F0;
}

html{
background: #fff;
}
*{
box-sizing: border-box;
}
body {
overflow: hidden;
margin: 0;
padding: 32px 24px 24px 24px;
box-sizing: border-box;
}

#seatBox {
  position: absolute;
  padding: 10px;
  overflow: auto;
}

#zy, #save, #download {
width: 80px;
height: 35px;
line-height: 25px;
margin-right: 20px;
border-radius: 2px;
background-color: #1A77F0;
text-align: center;
cursor: pointer;
color: white;
}

#pictures {
  width: 80px;
  height: 35px;
  line-height: 25px;
  margin-right: 20px;
  border-radius: 2px;
  background-color: #1A77F0;
  text-align: center;
  cursor: pointer;
  color: white;
}

#previewImage {
  position: absolute;
  min-width: 260px;
  min-height: 200px;
  background: #E8E8E8;
  right: 30px;
  top: 70%;
  z-index: 2;
}

canvas {
  width: 260px;
  height: 200px;
}

.seatItem {
	position: absolute;
	border: 0px solid #000;
	border-radius: 0;
	font-size: 12px;
	cursor: move;
	display: flex;
	justify-content: center;
	align-items: center;
  margin: 5px;
}

.btn-info:hover{
background: var(--color) !important;
border-color: var(--color) !important;
opacity: 0.8;
color: #FFF !important;
}

.opt {
  height: 30px;
  line-height: 20px;
  margin-left: 20px;
}

.optBtn {
  height: 30px;
  line-height: 20px;
  margin-left: 10px;
}

.layui-select-title {
  width: 100px;
}

.magic-radio, .magic-checkbox {
  position: absolute;
  display: none;
}

.magic-radio + label, .magic-checkbox + label {
  position: relative;
  display: block;
  padding-left: 30px;
  cursor: pointer;
}

.magic-radio:checked + label:before {
  border: 1px solid var(--color);
}

.magic-radio:checked + label:before, .magic-checkbox:checked + label:before {
  animation-name: none;
}
.magic-radio + label:before {
  border-radius: 50%;
}
.magic-radio + label:before, .magic-checkbox + label:before {
  position: absolute;
  top: 0;
  left: 0;
  display: inline-block;
  width: 20px;
  height: 20px;
  content: '';
  border: 1px solid #c0c0c0;
}

.magic-radio:checked + label:after, .magic-checkbox:checked + label:after {
  display: block;
}
.magic-radio + label:after {
  top: 7px;
  left: 7px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color);
}
.magic-radio + label:after, .magic-checkbox + label:after {
  position: absolute;
  display: none;
  content: '';
}

.container {
  display: flex;
}

.txt {
  width: 60px;
  text-align: end;
  padding-right: 5px;
  line-height: 40px;
}

.row {
  width: 30px;
}

h2{
  width: 100%;
  font-weight: 600;
  padding-left: 20px !important;
}

.titleBox {
  display: flex;
  margin-top: 10px;
}

.radioBox {
  height: 40px;
  border-bottom: 1px solid #E8E8E8;
}

.btnBox {
  height: 45px;
  padding-left: 20px;
  border-bottom: 1px solid #E8E8E8;
}

.boxBorder {
  border: none;
}

.luckysheet_info_detail {
  display: none !important;
}