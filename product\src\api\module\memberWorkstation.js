// 导入封装的方法
/* eslint-disable */
// let baseURL = "http://180.0.16.79:8081/lzt";
import Vue from "vue";
import { post, get, _post, postform, fileRequest, exportFile } from "../http";

const memberWorkstation = {
  getHomePageStatistics (params) { // 获取首页统计数据
    return post('/wygzsBillboard/getHomePageStatistics', params)
  },
  applySite (params) {
    return post('/dictionary/list', params)
  },
  //   工作室下拉框
  getStudio (params) {
    return post('/wygzsStudio/getStudio', params)
  },
  workstationWebURL () {
    // 工作站web端
    return JSON.parse(
      sessionStorage.getItem("workstationUrl" + Vue.prototype.$logo())
    );
  },
  dictionaryList (params) {
    // 界别、专委会列表
    return get(`/dictionary/list`, params);
  },
  workstationSupervise: {
    wygzsFileSaveFile (params) {
      // 委员工作站上传
      return postform("/wygzsFile/saveFile", params, { timeout: 80000 });
    },
    wygzsstudioList (params) {
      // 列表
      return get(`/wygzsStudio/list`, params);
    },
    exportStudio (params) { // 导出工作站
      return post(`/wygzsStudio/exportStudio`, params);
    },
    manageAdd (params) {  // 新增委员工作室
      return post(`/wygzsStudio/add`, params);
    },
    updateIsEnable (params) { // 修改启用状态
      return post(`/wygzsStudio/updateIsEnable`, params);
    },
    dels (params) { // 删除委员工作室
      return post(`/wygzsStudio/dels`, params);
    },
    info (params) { // 获取委员工作室详情
      return post(`/wygzsStudio/info/${params}`);
    },
    manageEdit (params) { // 修改委员工作室
      return post(`/wygzsStudio/edit`, params);
    },
    wygzsStudioMemberList (params) { // 工作室入驻委员列表
      return post(`/wygzsStudioMember/list`, params);
    },
    wygzsStudioMemberAdd (params) { // 新增工作室入驻委员
      return post(`/wygzsStudioMember/add`, params);
    },
    wygzsStudioMemberExport (params) { // 导出驻站代表信息
      return post(`/wygzsStudioMember/export`, params);
    },
    getadminList (params) { // 管理员查询工作室列表
      return post(`/wygzsStudio/adminList`, params);
    },
    officeonlinescheduleList (params) { //
      return post(`/wygzsDutyArrange/list`, params);
    },
    getStudio (params) {
      return post(`/wygzsStudio/getStudio`, params);
    },
    getStudioMemberSelect (params) {
      return post(`/wygzsStudioMember/getStudioMemberSelect`, params);
    },
    wygzsDutyArrangeAdd (params) {
      return post(`/wygzsDutyArrange/add`, params);
    },
    wygzsDutyArrangeDels (params) {
      return post(`/wygzsDutyArrange/dels`, params);
    },
    wygzsDutyArrangeInfo (params) {
      return post(`/wygzsDutyArrange/info/${params}`);
    },
    wygzsDutyArrangeEdit (params) {
      return post(`/wygzsDutyArrange/edit`, params);
    },
    wholeuserBatchDel (params) {
      return post(`/wygzsStudioMember/dels`, params);
    },
    delStudioMembers (params) {
      return post(`/wygzsStudioMember/delStudioMembers`, params);
    },
    wygzsStudioMemberSaveSort (params) {
      return _post(`/wygzsStudioMember/saveSort`, params);
    },
    wygzsReplyTitleList (params) {
      return post(`/wygzsReplyTitle/list`, params);
    },
    wygzsReplyTitleEditAuditStatus (params) {
      return post(`/wygzsReplyTitle/editAuditStatus`, params);
    },
    wygzsReplyTitleInfo (id) {
      return post(`/wygzsReplyTitle/info/${id}`);
    },
    wygzsReplyTitleEdit (params) {
      return post(`/wygzsReplyTitle/edit`, params);
    },
    wygzsReplyTitleAdd (params) {
      return post(`/wygzsReplyTitle/save`, params);
    },
    wygzsWorkRegimeAdd (params) {
      return post(`/wygzsWorkRegime/add`, params);
    },

    // 委员工作室制度
    wygzsWorkRegimeEdit (params) {
      return post(`/wygzsWorkRegime/edit`, params);
    },
    wygzsWorkRegimeInfo (params) {
      return post(`/wygzsWorkRegime/info/${params}`);
    },
    // 修改工作制度发布状态
    editIsPush (params) {
      return post(`wygzsWorkRegime/editIsPush`, params);
    },
    getWorkRegimeList (params) {
      // 委员工作室工作制度列表
      return post("/wygzsWorkRegime/list", params);
    },
    //  删除工作制度
    wygzsWorkRegimedels (params) {
      return post(`/wygzsWorkRegime/dels`, params);
    },
    wygzsReplyTitleDels (params) {
      return post(`/wygzsReplyTitle/dels`, params);
    },
    wygzsStudioGetStudioSelect (params) {
      return post(`/wygzsStudio/getStudioSelect`, params);
    },
    // 新闻咨询模块 我的咨询
    wygzsNewsInformationList (params) {
      return post(`/wygzsNewsInformation/listBySelf`, params);
    },
    wygzsNewsInformationAllList (params) {
      return post(`/wygzsNewsInformation/list`, params);
    },
    // 新增 bianji
    wygzsNewsInformation (url, params) {
      return post(url, params);
    },
    // 修改新闻资讯发布状态
    wygzsNewsInformationeditIsPuch (params) {
      return post(`/wygzsNewsInformation/editIsPuch`, params);
    },
    // 获取详情
    wygzsNewsInformationInfo (id) {
      return post(`/wygzsNewsInformation/info/${id}`);
    },
    // 删除
    wygzsNewsInformationDels (params) {
      return post(`/wygzsNewsInformation/dels`, params);
    },
    // 工作动态-列表
    wygzsWorkDynamicList (params) {
      return post(`/wygzsWorkDynamic/list`, params);
    },
    // 工作动态-新增
    wygzsWorkDynamicAdd (params) {
      return post(`/wygzsWorkDynamic/add`, params);
    },
    // 工作动态-详情
    wygzsWorkDynamicInfo (id) {
      return post(`/wygzsWorkDynamic/info/${id}`);
    },
    workRegimeInfo (params) {
      // 详情
      return post(`wygzsWorkRegime/info/${params}`);
    },
    // 工作动态-修改
    wygzsWorkDynamicEdit (params) {
      return post(`/wygzsWorkDynamic/edit`, params);
    },
    // 工作动态-修改发布状态
    wygzsWorkDynamicEditIsPuch (params) {
      return post(`/wygzsWorkDynamic/editIsPuch`, params);
    },
    // 工作动态删除
    wygzsWorkDynamicdelsh (params) {
      return post(`/wygzsWorkDynamic/dels`, params);
    },
    //大会专题列表
    meetSpecialList (params) {
      return post(`/meetSpecial/list`, params);
    },
    //删除大会专题
    meetSpecialDels (params) {
      return post(`/meetSpecial/dels`, params);
    },
    //大会专题详情
    meetSpecialInfo (id) {
      return post(`/meetSpecial/info/`, id);
    },
    //新增大会专题
    meetSpecialAdd (params) {
      return post(`/meetSpecial/add`, params, { 'Content-Type': 'application/json;charset=UTF-8' });
    },
    //编辑大会专题
    meetSpecialEdit (params) {
      return post(`/meetSpecial/edit`, params, { 'Content-Type': 'application/json;charset=UTF-8' });
    },
    //获取会议下拉框
    meetSpecialGetMeetByType (params) {
      return post(`/meetSpecial/getMeetByType`, params);
    },
    // 下载工作站导入模板
    downImportTemp (params) {
      fileRequest('wygzsStudio/downImportTemp', params, '工作站导入模板.xls')
    },
    // 导入工作站
    importStudio (params) {
      return postform('wygzsStudio/importStudio', params)
    },
    // 下载驻站代表信息导入模板
    downImportMemberTemp (params) {
      fileRequest('wygzsStudioMember/downImportTemp', params, '驻站代表信息导入模板.xls')
    },
    // 导入工作站入驻代表
    importStudioMember (params) {
      return postform('wygzsStudioMember/importStudioMember', params)
    }
  },
  // 视频会议配置
  getVideoMeetProvider (params) {
    return post('/wygzsRemoteConsultation/getVideoMeetProvider', params)
  },
  // 视频会议
  wygzsRemoteConsultationList (params) {
    return post('/wygzsRemoteConsultation/list', params)
  },
  wygzsRemoteConsultationDels (params) {
    return post('/wygzsRemoteConsultation/dels', params)
  },
  wygzsRemoteConsultation (url, params) {
    return post(url, params)
  },
  wygzsRemoteConsultationinfo (params) {
    return post('/wygzsRemoteConsultation/info/' + params)
  },


  // 履职管理
  generalInterface (interfaceName, params) {
    return post(interfaceName, params)
  },
  wygzsDutyGainlist (params) {
    return post('/wygzsDutyGain/list', params)
  },
  wygzsDutyGaindels (params) {
    return post('/wygzsDutyGain/dels', params)
  },
  wygzsDutyGaininfo (params) {
    return post('/wygzsDutyGain/info/' + params)
  },

  wygzsDutyGaineditPuchStatus (params) {
    return post('/wygzsDutyGain/editPuchStatus', params)
  },
  //  活动管理

  saveActivity (params) { // 新增编辑
    return post('/wygzsActivity/saveActivity', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  wygzsActivitylist (params) {
    return post('/wygzsActivity/list', params)
  },
  wygzsActivityinfo (params) { //  详情
    return post('/wygzsActivity/info/' + params)
  },
  downloadFile (params) { // 下载
    return exportFile('/wygzsFile/download?fileId=' + params)
  },
  // 下拉框枚举列表
  getEnumList (params) {
    return post('/wygzsCommon/getEnumList', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  operationTable (params) { //  发布 删除等
    return post('/wygzsCommon/operationTable', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  operationActivityUserByAdmin (params) { //  报名、请假、签到活动用户
    return post('/wygzsActivity/operationActivityUserByAdmin', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  getLeaveInfoByUser (params) { //  活动管理_请假详情
    return post('/wygzsActivityOperationRecord/getLeaveInfoByUser', params,)
  },
  countActivityUser (params) { // 管理员统计活动用户
    return post('/wygzsActivity/countActivityUser', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },

  queryActivityUser (params) { // 管理员统计活动用户
    return post('/wygzsActivity/queryActivityUser', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  queryOperationRecord (params) { //PC端管理员查询活动用户操作记录
    return post('/wygzsActivity/queryOperationRecord', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  sendSMSToActivityUser (params) { // PC端管理员发送短信给活动用户
    return post('/wygzsActivity/sendSMSToActivityUser', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  createSignQRCode (params) { // Pc端管理员生成活动签到二维码
    return post('/wygzsActivity/createSignQRCode', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  exportActivityUser (params, filename) { // PC端管理员导出活动用户数据
    return fileRequest('/wygzsActivity/exportActivityUser', params, filename)
  },

  replyContentList (params) {
    // 留言回复列表
    return get("wygszReplyContent/list", params);
  },
  replyExamineReply (params) {
    // 提醒
    return post("wygszReplyContent/examineReply", params);
  },
  replyDels (params) {
    //删除回复
    return post(`wygszReplyContent/dels`, params);
  },
  replyEditDetail (id) {
    //编辑
    return post(`wygszReplyContent/info/${id}`);
  },
  replyEdit (params) {
    //编辑
    return post("wygszReplyContent/edit", params);
  },
  reply (params) {
    //新增 留言回复
    return get("wygszReplyContent/reply", params);
  },
  replyUpload (params) {
    // 新增 留言回复文件上传
    return postform('wygzsFile/saveFile', params)
  },
  // 驻站代表信息列表
  getStationingDeputyList (params) {
    return postform('wygzsStudioMember/getStationingDeputyList', params)
  },
  // 驻站代表信息详情
  getStationingDeputyInfo (params) {
    return postform('wygzsStudioMember/getStationingDeputyInfo', params)
  },
  // 驻站代表信息列表导出
  exportStationInfo (params, filename) {
    return fileRequest('/wygzsStudioMember/exportStationingDeputyList', params, filename)
  },
  // 驻站代表联络站下拉框
  getStudioAll (params) {
    return postform('wygzsStudio/getStudioAll', params)
  },
  // 新闻资讯栏目列表
  newsColumnTypeList (params) {
    return post('/wygzsNewsInformation/newsColumnTypeList', params)
  },
  // 新闻资讯栏目新增
  addColumnTypeList (params) {
    return post('/wygzsNewsInformation/addColumnTypeList', params)
  },
  // 新闻资讯栏目编辑
  editColumnType (params) {
    return post('/wygzsNewsInformation/editColumnType', params)
  },
  // 新闻资讯栏目删除
  delColumnType (params) {
    return post('/wygzsNewsInformation/delColumnType', params)
  },
  // 活动类型列表
  getActivityTypeList (params) {
    return post('/wygzsActivity/getActivityTypeList', params)
  },
  // 活动类型新增
  addActivityTypeList (params) {
    return post('/wygzsActivity/addActivityTypeList', params)
  },
  // 活动类型编辑
  editActivityType (params) {
    return post('/wygzsActivity/editActivityType', params)
  },
  // 活动类型删除
  delActivityType (params) {
    return post('/wygzsActivity/delActivityType', params)
  },
  // 民情民意热点列表
  wygzsHotspotKeywordsList (params) {
    return post('/wygzsHotspotKeywords/list', params)
  },
  // 新增民情民意热点
  addHotspotKeywords (params) {
    return post('/wygzsHotspotKeywords/addHotspotKeywords', params)
  },
  //删除民情民意热点
  addHotspotKeywordsdels (params) {
    return post('/wygzsHotspotKeywords/dels', params)
  },
  // 驻站代表管理列表
  findAllStudioMembers (params) {
    return post('/wygzsStudioMember/findAllStudioMembers', params)
  },
  // 搜索用户
  wygzsStudioMemberuserList (params) {
    return post('/wygzsStudioMember/userList', params)
  },
  // 用户入驻多个工作站
  saveStudioMember (params) {
    return post('/wygzsStudioMember/saveStudioMember', params)
  },
  // 用户入驻多个工作站
  editStudioMember (params) {
    return post('/wygzsStudioMember/edit', params)
  },
  // 导出驻站代表信息
  exportStudioMembers (params, fileName) {
    return fileRequest('/wygzsStudioMember/exportStudioMembers', params, fileName)
  },
  currencyPost (url, params) { // 通用post
    return post(url, params)
  },
  currencyGet (url, params) { // 通用Get
    return get(url + params)
  }
}

export default memberWorkstation;
