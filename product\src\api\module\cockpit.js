import { post, get } from '../http'

const cockpit = {
  getToDayProposalSubmitCount (params) {
    return post('/proposalScreen/getToDayProposalSubmitCount', params)
  },
  getThisProposalSubmitCount (params) {
    return post('/proposalScreen/getThisProposalSubmitCount', params)
  },
  getProposalTypeCount (params) {
    return post('/proposalScreen/getProposalTypeCount', params)
  },
  getProposalTitle (params) {
    return post('/proposalScreen/getProposalTitle', params)
  },
  getProposalCategory (params) {
    return post('/proposalScreen/getProposalCategory', params)
  },
  // 大数据
  getCommonAggsByTags (params) {
    const theme = sessionStorage.getItem('theme')
    const BigDataLiveShowUrl = JSON.parse(sessionStorage.getItem('BigDataLiveShowUrl' + theme))
    return get(BigDataLiveShowUrl + '/rdchanping/common/aggsByTags', params)
  }
}
export default cockpit
