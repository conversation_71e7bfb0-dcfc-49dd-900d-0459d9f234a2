// 导入封装的方法
import {
  get,
  post,
  loginUc,
  exportFile,
  fileRequest,
  filedownload,
  postform
} from '../http'
const userCenter = {
  // 用户中心新增编辑
  userCenterAdd (url, params) {
    return post(`${loginUc}${url}`, params)
  },
  // 用户中心列表
  userCenterList (params) {
    return post(`${loginUc}/user/list?`, params)
  },
  // 用户中心用户详情
  userCenterInfo (params) {
    return post(`${loginUc}/user/info?`, params)
  },
  // 用户中心用户删除
  userCenterDel (params) {
    return post(`${loginUc}/user/del?`, params)
  },
  // 用户中心禁用用户
  userCenterUnusing (params) {
    return post(`${loginUc}/user/edit/unusing?`, params)
  },
  // 用户中心启用用户
  userCenterUsing (params) {
    return post(`${loginUc}/user/edit/using?`, params)
  },
  // 用户中心修改用户密码
  userCenterPassword (params) {
    return post(`${loginUc}/user/edit/password?`, params)
  },
  // 用户中心地区树
  userCenterAreaTree (params) {
    return post(`${loginUc}/area/tree`, params)
  },
  // 用户中心地区树
  userCenterAreaAllTree (params) {
    return post(`${loginUc}/area/allTree`, params)
  },
  // 业务分组树
  groupingTree (params) {
    return post(`${loginUc}/grouping/tree`, params)
  },
  // 业务分组删除
  groupingDel (params) {
    return post(`${loginUc}/grouping/del?`, params)
  },
  // 业务分组详情
  groupingInfo (params) {
    return post(`${loginUc}/grouping/info?`, params)
  },
  // 获取地区内用户
  treeUser (params) {
    return post(`${loginUc}/user/treeUser?`, params)
  },
  // 获取当分组已选人员
  userGroupTree (params) {
    return post(`${loginUc}/grouping/userGroupTree?`, params)
  },
  // 用户上报列表
  findReportUsers (params) {
    return post('/wholeuser/findReportUsers', params)
  },
  // 用户上报
  addReportUser (params) {
    return post('/wholeuser/addReportUser', params)
  },
  // 用户上报检查
  checkReportUser (params) {
    return post('/wholeuser/checkReportUser', params)
  },
  // 用户上报修改
  userUpdateReportUser (params) {
    return post(`${loginUc}/report/user/updateReportUser`, params)
  },
  // 用户上报修改详情
  reportUserInfo (params) {
    return post(`${loginUc}/report/user/info/${params}`)
  },
  // 用户上报修改检查
  userCheckReportUser (params) {
    return post(`${loginUc}/report/user/checkReportUser`, params)
  },
  // 用户上报审核数量
  reportUserCount (params) {
    return post(`${loginUc}/report/user/count`, params)
  },
  // 用户上报地区数量
  reportUserTree (params) {
    return post(`${loginUc}/report/user/tree`, params)
  },
  // 用户上报审核列表
  reportUserList (params) {
    return post(`${loginUc}/report/user/list`, params)
  },
  // 用户上报审核列表
  reportUserAuditUser (params) {
    return post(`${loginUc}/report/user/auditUser`, params)
  },
  // 用户上报审核列表
  reportUserMergeUser (params) {
    return post(`${loginUc}/report/user/mergeUser`, params)
  },
  // 获取菜单开通地区
  getmenuareas (params) {
    return post('/menuarea/getmenuareas', params)
  },
  // 菜单开通地区保存/更新
  savemenuareas (params) {
    return post('/menuarea/savemenuareas', params)
  },
  // 获取菜单开通地区
  getmoduleareas (params) {
    return post('/module/getmoduleareas', params)
  },
  // 菜单开通地区保存/更新
  savemoduleareas (params) {
    return post('/module/savemoduleareas', params)
  },
  // 用户上报审核列表
  resetPassword (params) {
    return post(`${loginUc}/user/reset/password`, params)
  },
  // 用户上报审核列表
  userCenterModifyLogList (params) {
    return post(`${loginUc}/userModifyLog/list?`, params)
  },
  // 用户上报审核列表
  userCenterModifyLogInfo (params) {
    return post(`${loginUc}/userModifyLog/info?`, params)
  },
  // 用户上报审核列表
  delReportUser (params) {
    return post(`${loginUc}/report/user/delReportUser`, params)
  },
  // 下载模板用户中心
  userCenterImportemplate (params) {
    fileRequest(`${loginUc}/user/importemplate`, params, '用户导入模板.xlsx')
  },
  // 用户中心导入预览
  importPreview (params) {
    return postform(`${loginUc}/user/importPreview`, params)
  },
  // 用户中心导入
  importUser (params) {
    return filedownload(`${loginUc}/user/importUser`, params)
  },
  // 下载模板用户中心
  reportUserCenterImportemplate (params) {
    fileRequest('/report/user/importemplate', params, '用户导入模板.xlsx')
  },
  // 用户中心导入预览
  reportImportPreview (params) {
    return postform('/report/user/importPreview', params)
  },
  // 用户中心导入
  reportImportUser (params) {
    return filedownload('/report/user/importUser', params)
  },
  // 用户上报审核列表
  delSameUser (params) {
    return post(`${loginUc}/report/user/delSameUser`, params)
  },
  // 用户上报审核列表
  oneMergeUser (params) {
    return post(`${loginUc}/report/user/oneMergeUser`, params)
  },
  // 批量认证姓名相同 其他不同的
  errorPassReportUsers (params) {
    return post(`${loginUc}/report/user/errorPassReportUsers`, params)
  },
  // 批量忽略
  ignoreReportUsers (params) {
    return post(`${loginUc}/report/user/ignoreReportUsers`, params)
  }
}
const systemSettings = {
  ...userCenter,
  generalAdd (url, params) {
    return post(url, params)
  },
  poinexistsids (type, params) {
    return post(`/poinexistsids/${type}`, params)
  },
  // 菜单和权限接口
  menuTree (params) {
    return post('/menu/tree', params)
  },
  menudel (params) {
    return post(`/menu/del/${params}`)
  },
  menuInfo (params) {
    return post(`/menu/info/${params}`)
  },
  menuAuths (params) {
    return post('/menu/auths?', params)
  },
  // 角色接口
  roleList (params) {
    return post('/role/list?', params)
  },
  roleDel (params) {
    return post('/role/del', params)
  },
  roleInfo (params) {
    return post(`/role/info/${params}`)
  },
  systemfeedbackList (params) {
    return post('/systemfeedback/list?', params)
  },
  systemfeedbackDels (params) {
    return post('/systemfeedback/dels', params)
  },
  systemfeedbackInfo (params) {
    return post(`/systemfeedback/info/${params}`)
  },
  systemfeedbackreplyAdd (params) {
    return post('/systemfeedbackreply/add?', params)
  },
  systemfeedbackreplyAEdit (params) {
    return post('/systemfeedbackreply/edit?', params)
  },
  systemfeedbackreplyList (params) {
    return post('/systemfeedbackreply/list?', params)
  },
  systemfeedbackreplyDels (params) {
    return post('/systemfeedbackreply/dels', params)
  },
  systemfeedbackreplyInfo (params) {
    return post(`/systemfeedbackreply/info/${params}`)
  },
  menutreeWithrole (params) {
    return post('/role/menutree/withrole?', params)
  },
  roleSavemenu (params) {
    return post('/role/savemenu?', params)
  },
  menutreeForole (params) {
    return post('/role/menutree/forole?', params)
  },
  roleChooseauths (params) {
    return post('/role/chooseauths', params)
  },
  roleSaveauth (params) {
    return post('/role/saveauth', params)
  },
  roleSaveuser (params) {
    return post('/role/saveuser?', params)
  },
  roleWithuser (params) {
    return post('/role/info/withuser?', params)
  },
  roleRemoveuser (params) {
    return post('/role/removeuser', params)
  },
  roleMinisetAuths (params) {
    return post('/role/miniset/auths', params)
  },
  roleMiniset (params) {
    return post('/role/miniset', params)
  },
  dataFind (params) {
    return post('/role/data/find?', params)
  },
  dataSave (params) {
    return post('/role/data/save?', params)
  },
  modulerolelist (params) {
    return post('/module/rolelist?', params)
  },
  saverolemodules (params) {
    return post('/module/saverolemodules?', params)
  },
  saveH5rolemodules (params) {
    return post('/sysmoduleapp/saverolemodules?', params)
  },
  // 最新版H5模块权限修改
  saveNewH5rolemodules (params) {
    return post('/homepage/addRoleModules?', params)
  },
  H5rolelist (params) {
    return post('/sysmoduleapp/rolelist?', params)
  },
  // 最新版H5模块权限列表
  newH5rolelist (params) {
    return post('/homepage/roleList?', params)
  },
  // 字典接口
  dictionaryList (params) {
    return post('/dictionary/list?', params)
  },
  dictionaryType (params) {
    return post('/dictionary/types', params)
  },
  dictionaryInfo (params) {
    return post(`/dictionary/info/${params}`)
  },
  dictionaryDel (params) {
    return post(`/dictionary/del/${params}`)
  },
  dictionaryPubkvs (params) {
    return post('/dictionary/pubkvs?', params)
  },
  // 树接口
  treeList (params) {
    return post('/tree/list?', params)
  },
  treeType (params) {
    return post('/tree/types', params)
  },
  treeInfo (params) {
    return post(`/tree/info/${params}`)
  },
  treeDel (params) {
    return post(`/tree/del/${params}`)
  },
  // 用户接口
  userList (params) {
    return post('/wholeuser/list?', params)
  },
  userDel (params) {
    return post('/wholeuser/batch/del', params)
  },
  userInfo (params) {
    return post(`/wholeuser/info/${params}`)
  },
  workuserInfo (params) { // 工作站代表编辑
    return post(`/work/station/manage/user/info/${params}`)
  },
  userEnable (params) {
    return post('/wholeuser/batch/startuse', params)
  },
  userDisable (params) {
    return post('/wholeuser/batch/stopuse', params)
  },
  tagroups (params) {
    return post('/wholeuser/tagroups', params)
  },
  randmobile (params) {
    return post('/wholeuser/randmobile', params)
  },
  buildaccount (params) {
    return post('/wholeuser/buildaccount?', params)
  },
  finduser (params) {
    return post('/wholeuser/finduser', params)
  },
  useroles (params) {
    return post('/role/useroles?', params)
  },
  closeReceiveMsg (params) {
    return post('/wholeuser/batch/closeReceiveMsg?', params)
  },
  // 标签管理
  labeluserLabels (params) {
    return post('/labeluser/labels', params)
  },
  labeluserUsers (params) {
    return post('/labeluser/users?', params)
  },
  labelSaveuser (params) {
    return post('/labeluser/saveuser', params)
  },
  lebUsredel (params) {
    return post('/labeluser/removeuser', params)
  },
  // 配置管理
  sysconfig (params) {
    return post('/sysconfig', params)
  },
  delconfig (params) {
    return post('/delconfig?', params)
  },
  // 选人管理
  labeluserPoints (params) {
    return post('/labeluser/points', params)
  },
  labeluserPointlabel (params) {
    return post('/labeluser/pointlabel?', params)
  },
  // 日志接口
  syslog (params) {
    return post('/syslog', params)
  },
  // 日志详情
  syslogInfo (params) {
    return post(`/syslog/info/${params}`)
  },
  // 登录日志接口
  loginlog (params) {
    return post('/loginlog?', params)
  },
  // 错误日志接口
  errorlog (params) {
    return post('/errorlog', params)
  },
  // 任务接口
  taskList (params) {
    return post('/task/list', params)
  },
  taskOnoff (params) {
    return post('/task/onoff', params)
  },
  // 用户关系管理
  userelationType (params) {
    return post('/userelation/types', params)
  },
  userelationTree (params) {
    return post('/userelation/tree?', params)
  },
  userelationSave (params) {
    return post('/userelation/save?', params)
  },
  setList (params) {
    return post('/smsSend/setList', params)
  },
  smsSendList (params) {
    return post('/smsSend/list', params)
  },
  smsSendInfo (params) {
    return post(`/smsSend/info/${params}`)
  },
  smsSenddDel (params) {
    return post('/smsSend/dels', params)
  },
  sendVCode (params) {
    return get('/smsSend/sendVcode', params)
  },
  smstemplate (params) {
    return post('/smstemplate/list?', params)
  },
  smstemplateInfo (params) {
    return post(`/smstemplate/info/${params}`)
  },
  smstemplateDel (params) {
    return post('/smstemplate/dels', params)
  },
  smsreceived (params) {
    return post('/smsreceived/list?', params)
  },
  smsreceivedInfo (params) {
    return post(`/smsreceived/info/${params}`)
  },
  smsreceivedDel (params) {
    return post('/smsreceived/dels', params)
  },
  // 下载模板
  importemplate (params) {
    exportFile('/wholeuser/importemplate', params)
  },
  // 导入代表或全国代表
  import (params, text) {
    fileRequest('/wholeuser/import', params, text)
  },
  // 下载模板
  companyuserImportemplate (params) {
    exportFile('/companyuser/importemplate', params)
  },
  // 导入代表或全国代表
  companyuserImport (params, text) {
    fileRequest('/companyuser/import', params, text)
  },
  // 轮播图列表
  loginimgList (params, text) {
    return post('/loginimg/list', params, text)
  },
  // 轮播图删除
  loginimgDel (params, text) {
    return post('/loginimg/del?', params, text)
  },
  userModifyLogList (params) {
    return post('/userModifyLog/list?', params)
  },
  userModifyLogInfo (params) {
    return post('/userModifyLog/info', params)
  },
  roleAttachSave (params) {
    return post('/role/attach/save?', params)
  },
  // 重置密码
  batchResetpwd (params) {
    return post('/wholeuser/batch/resetpwd', params)
  },
  verifyenableList (params) {
    return post('/verifyenable/list?', params)
  },
  verifyenableInfo (params) {
    return post('/verifyenable/info?', params)
  },
  verifyenableDel (params) {
    return post('/verifyenable/del?', params)
  },
  quickmenuList (params) {
    return post('/quickmenu/list?', params)
  },
  quickmenuInfo (params) {
    return post(`/quickmenu/info/${params}`)
  },
  quickmenuDel (params) {
    return post('/quickmenu/dels?', params)
  },
  menuroles (params) {
    return post('/quickmenu/menuroles?', params)
  },
  appimageGetType (params) {
    return post('/appimage/getType?', params)
  },
  appimageList (params) {
    return post('/appimage/list?', params)
  },
  appimageInfo (params) {
    return post(`/appimage/info/${params}`)
  },
  appimageDel (params) {
    return post('/appimage/dels?', params)
  },
  getDefaultImage (params) {
    return post('/appimage/getDefaultImage?', params)
  },
  menutreeQuickMuen (params) {
    return post('/quickmenu/menutree/quickMenu', params)
  },
  clienterList (params) {
    return post('/clienter/list', params)
  },
  clienterInfo (params) {
    return post(`/clienter/info/${params}`)
  },
  clienterDel (params) {
    return post('/clienter/dels', params)
  },
  addClienterJoin (params) {
    return post('/sysapi/addClienterJoin', params)
  },
  delClienterJoin (params) {
    return post('/sysapi/delClienterJoin', params)
  },
  sysapiList (params) {
    return post('/sysapi/list', params)
  },
  sysapiInfo (params) {
    return post(`/sysapi/info/${params}`)
  },
  sysapiDel (params) {
    return post('/sysapi/dels', params)
  },
  getUnderTreeByArea (params) { // 查询当前和下级开通地区
    return get(`/area/getUnderTreeByArea?areaId=${params}`)
  },
  // 首页飘窗增删改查
  HomewindowList (params) {
    return post('/homepageconfig/list', params)
  },
  HomewindowAddEdit (url, params) {
    return post(url, params)
  },
  HomewindowDels (params) {
    return post('/homepageconfig/dels', params)
  },
  HomewindowInfo (params) {
    return post(`/homepageconfig/info/${params}`)
  },
  // 首页海报管理
  homePosterList (params) {
    return post('/poster/list', params)
  },
  homePosterAddEdit (url, params) {
    return post(url, params)
  },
  homePosterDels (params) {
    return post('/poster/dels', params)
  },
  homePosterDel (params) {
    return post(`/poster/del/${params}`)
  },
  homePosterInfo (params) {
    return post(`/poster/info/${params}`)
  },
  // 履职统计配置
  deputysystemdateList (params) {
    return post('/deputysystemdate/list', params)
  },
  dutyConfigNumDataBackups (params) {
    return post('/dutyConfigNum/dataBackups', params)
  },
  dutyConfigNumDataBackupsList (params) {
    return post('/dutyConfigNum/dataBackupsList', params)
  },
  deputysystemdateAddEdit (url, params) {
    return post(url, params)
  },
  deputysystemdateDels (params) {
    return post('/deputysystemdate/dels', params)
  },
  deputysystemdateDel (params) {
    return post(`/deputysystemdate/del/${params}`)
  },
  deputysystemdateInfo (params) {
    return post(`/deputysystemdate/info/${params}`)
  },
  // 履职管理
  sysinforeviewerList (params) {
    return post('/sysinforeviewer/list', params)
  },
  sysinforeviewerAddEdit (url, params) {
    return post(url, params)
  },
  sysinforeviewerDels (params) {
    return post('/sysinforeviewer/dels', params)
  },
  sysinforeviewerAddUser (params) {
    return post('/sysinforeviewer/addUser', params)
  },
  sysinforeviewerGetUser (params) {
    return post('/sysinforeviewer/getUser', params)
  },
  sysinforeviewerDel (params) {
    return post(`/sysinforeviewer/del/${params}`)
  },
  sysinforeviewerInfo (params) {
    return post(`/sysinforeviewer/info/${params}`)
  },
  sysinforeviewerAddEnterUser (params) {
    return post('/sysinforeviewer/addEnterUser', params)
  },
  sysinforeviewerGetEnterUser (params) {
    return post('/sysinforeviewer/getEnterUser', params)
  },
  sysinforeviewerUpdateAuth (params) {
    return post('/sysinforeviewer/updateAuth', params)
  },
  // 配置管理
  effectconfigList (params) {
    return post('/effectconfig/list', params)
  }
}
export default systemSettings
