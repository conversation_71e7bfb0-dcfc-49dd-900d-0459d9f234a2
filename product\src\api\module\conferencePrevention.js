import { post, exportFile } from '../http'

const conferencePrevention = {
  // 人员信息库列表
  personnelList (params) {
    return post('/esuserinfo/list', params)
  },
  // 人员信息库详情
  personnelInfo (id) {
    return post(`/esuserinfo/info/${id}`)
  },
  // 人员信息库新增与编辑
  personnelAdd (url, params) {
    return post(url, params)
  },
  // 人员信息库删除
  personnelDels (params) {
    return post('/esuserinfo/dels', params)
  },
  // 导出模板
  personnelImportemplate (params) {
    return exportFile('/esuserinfo/importemplate', params)
  },
  personnelImport (params) {
    return post('/esuserinfo/import', params)
  },
  // 个体健康信息表列表
  healthMonitoringList (params) {
    return post('/espersonalhealthinfo/list', params)
  },
  // 个体健康信息表详情
  healthMonitoringInfo (id) {
    return post(`/espersonalhealthinfo/info/${id}`)
  },
  // 个体健康信息表新增与编辑
  healthMonitoringAddEdit (url, params) {
    return post(url, params)
  },
  // 个体健康信息表删除
  healthMonitoringDels (params) {
    return post('/espersonalhealthinfo/dels', params)
  },
  // 疫苗接种、健康码、核酸删除
  esepidemicinfoDels (params) {
    return post('/esepidemicinfo/dels', params)
  },
  // 疫苗接种、健康码、核酸列表
  esepidemicinfoList (params) {
    return post('/esepidemicinfo/list', params)
  },
  // 疫苗接种、健康码、核酸详情
  esepidemicinfoInfo (id) {
    return post(`/esepidemicinfo/info/${id}`)
  },
  // 更新核酸
  sendData (params) {
    return post('/esepidemicinfo/sendData', params)
  }
}

export default conferencePrevention
