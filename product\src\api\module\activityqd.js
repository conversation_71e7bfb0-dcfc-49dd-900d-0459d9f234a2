// 导入封装的方法
import { get, post } from '../http'

const activity = {
  add (params) {
    return post('/activityupgrade/saveActivity', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  info (id) {
    return get('/activityupgrade/getActivityDetail', { id })
  },
  list (params) {
    return post('/activityupgrade/findActivitys', params)
  },
  myList (params) {
    return post('/activityupgrade/findMyActivitys', params)
  },
  // 是否app显示
  verifyShowApp (params) {
    return post('/activity/updateIsAppShow', params)
  },
  // 发送短信提醒
  sendMsg (params) {
    return post('/activity/smsSend', params)
  },
  dels (ids) {
    return post('/activity/dels', { ids })
  },
  // 二维码地址
  qrcode (id) {
    return get('/activityupgrade/getQrCode', { id })
  },
  // 考勤 tab类别
  attendanceType (id) {
    return post('/activityupgrade/getAttendanceHeader', { activityId: id })
  },
  // 考勤列表
  attendanceList (params) {
    return post('/activityupgrade/getAttendanceListVos', params)
  },
  // 报名
  signUp (params) {
    return post('/activityupgrade/addSignUpJoinUser', params)
  },
  // 我的报名
  mySignUp (params) {
    return post('/activityupgrade/addMySignUp', params)
  },
  // 取消报名和签到
  cancelSign (params) {
    return post('/activityupgrade/delJoinUser', params)
  },
  // 签到
  signIn (params) {
    return post('/activityupgrade/addSignInJoinUser', params)
  },
  // 活动资料
  material (params) {
    return post('/activityupgrade/getMaterainfoList', params)
  },
  materialAdd (params) {
    return post('/activityupgrade/saveMaterainfo', params)
  },
  materialDels (ids) {
    return post('/materiainfo/dels', { ids })
  },
  // 活动资料是否app显示
  materialShowApp (params) {
    return post('/materiainfo/editIsAppShow', params)
  },
  // 活动补录
  makeUpAdd (params) {
    return post('/activityupgrade/saveActivityFill', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  // 活动列表
  makeUpList (params) {
    return post('/activityupgrade/findFillActivitys', params)
  },
  // 考勤 报名签到 通知
  attendanceSendMsg (params) {
    return post('/activityupgrade/smsSend', params)
  },
  // 请假
  leave: {
    list (params) {
      return post('/leave/list', params)
    },
    verify (params) {
      return post('/leave/editStatus', params)
    },
    dels (ids) {
      return post('/leave/dels', { ids })
    },
    add (params) {
      return post('/activityupgrade/addLeave', params)
    },
    info (params) {
      return post(`/leave/info/${params}`)
    }
  }
}

export default activity
