import { post } from '../http'

const paperlessMeeting = {
  // 会议通知列表
  conferencenoticeList (params) {
    return post('/conferenceNotice/list', params)
  },
  // 会议通知详情
  conferencenoticeInfo (id) {
    return post(`/conferenceNotice/info/${id}`)
  },
  // 会议通知新增、修改
  conferenceNoticeadd (url, params) {
    return post(url, params)
  },
  // 会议通知删除
  conferenceNoticedels (params) {
    return post('/conferenceNotice/dels', params)
  },
  // 发送参会通知
  conferenceNoticeSendSms (url, params) {
    return post(url, params)
  },
  // 会议议程列表
  meetingScheduleList (params) {
    return post('/conferenceschedule/list', params)
  },
  // 会议议程新增编辑
  meetingScheduleAddEdit (url, params) {
    return post(url, params)
  },
  // 会议议程详情
  meetingScheduleInfo (id) {
    return post(`/conferenceschedule/appInfo/${id}`)
  },
  // 会议议程删除
  meetingScheduleDels (params) {
    return post('/conferenceschedule/dels', params)
  },
  // 会议文件列表
  conferencefileList (params) {
    return post('/conferencefile/list', params)
  },
  // 会议文件新增
  conferencefileAdd (url, params) {
    return post(url, params)
  },
  // 会议文件排序
  conferencefileUpdateFileSort (url, params) {
    return post(url, params)
  },
  // 会议文件排序
  conferencefileDels (params) {
    return post('/conferencefile/dels', params)
  },
  // 获取短信模板
  getSmsTemplate (params) {
    return post('/conferenceNotice/getSmsTemplate', params)
  }
}
export default paperlessMeeting
