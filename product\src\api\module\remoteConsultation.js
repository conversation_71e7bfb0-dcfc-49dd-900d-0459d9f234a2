// 导入封装的方法
import {
  get,
  post,
  postform,
  // fileRequest,
  exportFile
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
export const exe = params => exportFile('', params)

const remoteConsultation = {
  // 远程协商管理列表
  remoteConsultationList (params) {
    return post('/remoteconsultation/list', params)
  },
  // 远程协商管理列表详情
  remoteConsultatioInfo (params) {
    return post('/remoteconsultation/info/' + params)
  },
  // 保存远程协商
  remoteConsultatioAdd (params) {
    return post('/remoteconsultation/saveRemoteConsultation', params)
  },
  // 删除远程协商
  remoteConsultationDel (params) {
    return post('/remoteconsultation/dels', params)
  },
  // 远程协商会议类型
  remoteConsultationMeetType (params) {
    return post('/tree/list?treeType=10', params)
  },
  // 远程协商留言
  remoteConsultationMessage (params) {
    return post('/comment/save', params)
  },
  // 远程协商导出Excel
  remoteExport (params) {
    return exportFile('/remoteconsultation/export', params)
  },
  yilianpost (url, params, headers) {
    return post(url, params, headers)
  },
  // 远程协商导出
  outRemoteConsultationDetailWord (params) {
    exportFile('/remoteconsultation/outRemoteConsultationDetailWord', params)
  },
  // 意见征集和会议快讯列表
  activityreportList (params) {
    return post('/activityreport/list?', params)
  },
  // 意见征集和会议快讯详情
  activityreportinfo (params) {
    return post(`/activityreport/info/${params}`)
  },
  // 意见征集和会议快讯删除
  activityreportdel (params) {
    return post('/activityreport/dels', params)
  },
  // 意见征集和会议快讯审核
  editByIsCheck (params) {
    return post('/activityreport/editByIsCheck', params)
  },
  replyList (data) {
    return post('/comment/list', data)
  },
  // 评论删除
  replyDelete (data) {
    return post('/comment/dels', data)
  },
  // 评论审核
  replyVerify (data) {
    return post('/comment/audit', data)
  },
  // 评论编辑
  replyEdit (data) {
    return post('/comment/save', data)
  }
}

export default remoteConsultation
