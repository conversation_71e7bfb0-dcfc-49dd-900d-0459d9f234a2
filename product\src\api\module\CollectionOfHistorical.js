// 导入封装的方法
import {
  post
} from '../http'

const CollectionOfHistorical = {
  linkagestructure: {
    list(params) {
      return post('/linkagestructure/list', params)
    },
    info(params) {
      return post('/linkagestructure/info/' + params)
    },
    add(params) {
      return post('/linkagestructure/add', params)
    },
    edit(params) {
      return post('/linkagestructure/edit', params)
    },
    del(params) {
      return post('/linkagestructure/del/' + params)
    },
    dels(params) {
      return post('/linkagestructure/dels', params)
    },
    tree(params) {
      return post('/linkagestructure/tree', params)
    }
  },
  linkagehistoricalcollect: {
    list(params) {
      return post('/linkagehistoricalcollect/list', params)
    },
    myList(params) {
      return post('/linkagehistoricalcollect/myList', params)
    },
    info(params) {
      return post('/linkagehistoricalcollect/info/' + params)
    },
    add(params) {
      return post('/linkagehistoricalcollect/add', params)
    },
    del(params) {
      return post('/linkagehistoricalcollect/del/' + params)
    },
    dels(params) {
      return post('/linkagehistoricalcollect/dels', params)
    },
    edit(params) {
      return post('/linkagehistoricalcollect/edit', params)
    },
    count(params) {
      return post('/linkagehistoricalcollect/count', params)
    },
    editAuditState(params) {
      return post('/linkagehistoricalcollect/editAuditState', params)
    },
    editIsPublish(params) {
      return post('/linkagehistoricalcollect/editIsPublish', params)
    },
    sendShortCode(params) {
      return post('/linkagehistoricalcollect/sendShortCode', params)
    },
    checkVerifyCode(params) {
      return post('/linkagehistoricalcollect/checkVerifyCode', params)
    },
    webAdd(params) {
      return post('/linkagehistoricalcollect/webAdd', params)
    },
    noticeList(params) {
      return post('/linkagehistoricalcollect/notice/list', params)
    }
  }
}
export default CollectionOfHistorical
