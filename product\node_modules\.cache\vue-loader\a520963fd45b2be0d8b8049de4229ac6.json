{"remainingRequest": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\jnzx\\product\\src\\views\\app-management\\committee-data\\components\\committeeDataDetails.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\jnzx\\product\\src\\views\\app-management\\committee-data\\components\\committeeDataDetails.vue", "mtime": 1762767300301}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\jnzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdjb21taXR0ZWVEYXRhRGV0YWlscycsDQogIGRhdGEgKCkgew0KICAgIHJldHVybiB7DQogICAgICBkZXRhaWxzOiB7fQ0KICAgIH0NCiAgfSwNCiAgcHJvcHM6IFsnaWQnXSwNCiAgbW91bnRlZCAoKSB7DQogICAgdGhpcy5pbmZvcm1hdGlvbkxpc3RJbmZvKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFzeW5jIGluZm9ybWF0aW9uTGlzdEluZm8gKCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgdGhpcy4kYXBpLmFwcE1hbmFnZW1lbnQuaW5mb3JtYXRpb25MaXN0SW5mbyh0aGlzLmlkKQ0KICAgICAgdmFyIHsgZGF0YSB9ID0gcmVzDQogICAgICB0aGlzLmRldGFpbHMgPSBkYXRhDQogICAgICBjb25zb2xlLmxvZyh0aGlzLmRldGFpbHMuYXR0YWNoTGlzdCkNCiAgICB9LA0KICAgIEZpbGVQcmV2aWV3IChyb3cpIHsNCiAgICAgIGNvbnN0IHJvdXRlRGF0YSA9IHRoaXMuJHJvdXRlci5yZXNvbHZlKHsgcGF0aDogJy9GaWxlUHJldmlldycsIHF1ZXJ5OiB7IGlkOiByb3cuaWQsIGZpbGVVcmw6IHJvdy5maWxlUGF0aCwgZmlsZU5hbWU6IHJvdy5maWxlTmFtZSwgZmlsZVR5cGU6IHJvdy5maWxlVHlwZSwgcm91dGU6ICcvRmlsZVByZXZpZXcnIH0gfSkNCiAgICAgIHdpbmRvdy5vcGVuKHJvdXRlRGF0YS5ocmVmLCAnX2JsYW5rJykNCiAgICB9LA0KICAgIGZpbGVDbGljayAocm93KSB7DQogICAgICB0aGlzLiRhcGkucHJvcG9zYWwuZG93bmxvYWRGaWxlKHsgaWQ6IHJvdy5pZCB9LCByb3cuZmlsZU5hbWUpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["committeeDataDetails.vue"], "names": [], "mappings": ";AA4CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "committeeDataDetails.vue", "sourceRoot": "src/views/app-management/committee-data/components", "sourcesContent": ["<template>\r\n  <div class=\"committeeDataDetails details\">\r\n    <div class=\"details-title\">详情</div>\r\n    <div class=\"details-item-box\">\r\n      <div class=\"details-item-title\">\r\n        <div class=\"details-item-label\">标题</div>\r\n        <div class=\"details-item-value\">{{ details.title }}</div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">所属栏目</div>\r\n          <div class=\"details-item-value\">{{ details.structureName }}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">发布人</div>\r\n          <div class=\"details-item-value\">{{ details.createBy }}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">来源</div>\r\n          <div class=\"details-item-value\">{{ details.source }}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">发布时间</div>\r\n          <div class=\"details-item-value\">{{ details.publishDate }}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">附件</div>\r\n        <div class=\"details-item-value\">\r\n          <template v-if=\"details.attachmentList\">\r\n            <div class=\"details-item-file\"\r\n              v-for=\"(item, index) in details.attachmentList.filter(data => data.moduleType !== 'content')\"\r\n              :key=\"index\">{{ item.fileName }} <span @click=\"FilePreview(item)\">预览</span> <span\r\n                @click=\"fileClick(item)\">下载</span></div>\r\n          </template>\r\n        </div>\r\n      </div>\r\n      <div class=\"details-content\" v-html=\"details.content\"></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'committeeDataDetails',\r\n  data () {\r\n    return {\r\n      details: {}\r\n    }\r\n  },\r\n  props: ['id'],\r\n  mounted () {\r\n    this.informationListInfo()\r\n  },\r\n  methods: {\r\n    async informationListInfo () {\r\n      const res = await this.$api.appManagement.informationListInfo(this.id)\r\n      var { data } = res\r\n      this.details = data\r\n      console.log(this.details.attachList)\r\n    },\r\n    FilePreview (row) {\r\n      const routeData = this.$router.resolve({ path: '/FilePreview', query: { id: row.id, fileUrl: row.filePath, fileName: row.fileName, fileType: row.fileType, route: '/FilePreview' } })\r\n      window.open(routeData.href, '_blank')\r\n    },\r\n    fileClick (row) {\r\n      this.$api.proposal.downloadFile({ id: row.id }, row.fileName)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.committeeDataDetails {\r\n  height: 100%;\r\n  padding: 24px;\r\n\r\n  .details-content {\r\n    width: 100%;\r\n    padding: 40px;\r\n\r\n    img {\r\n      max-width: 700px;\r\n      margin: auto;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}