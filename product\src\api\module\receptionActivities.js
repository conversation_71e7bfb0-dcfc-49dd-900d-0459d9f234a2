// 导入封装的方法
import { post } from '../http'
export default {
  list (params) {
    return post('/ZyReceptionActivities/list', params)
  },
  add (url, params) {
    return post(url, params)
  },
  info (params) {
    return post('/ZyReceptionActivities/info/' + params)
  },
  del (params) {
    return post('/ZyReceptionActivities/del/' + params)
  },
  dels (params) {
    return post('/ZyReceptionActivities/dels', params)
  },
  getActivitiesSynopsis (params) {
    return post('/ZyReceptionActivities/activitiesSynopsis', params)
  },
  getStatistics (params) {
    return post('/ZyReceptionActivities/statistics', params)
  }
}
