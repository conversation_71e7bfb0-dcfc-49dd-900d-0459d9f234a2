// 导入封装的方法
import { post } from '../http'
export default {
  // 月度
  // 管理列表
  monthlyworkplanList (params) {
    return post('/monthlyworkplan/list', params)
  },
  monthlyworkplanInfo (params) {
    return post(`/monthlyworkplan/info/${params}`)
  },
  monthlyworkplanAddEdit (url, params) {
    return post(url, params)
  },
  monthlyworkplanDels (params) {
    return post('/monthlyworkplan/dels', params)
  },
  // 获取启动月份的回显
  monthGetStartDate (params) {
    return post('/monthlyworkplan/getStartDate', params)
  },
  // 修改启动月份
  monthUpdateStartDate (params) {
    return post('/monthlyworkplan/updateStartDate', params)
  },
  // 佐证列表
  monthlyworkplanwitnessList (params) {
    return post('/monthlyworkplanwitness/list', params)
  },
  // 佐证详情
  monthlyworkplanwitnessInfo (params) {
    return post(`/monthlyworkplanwitness/info/${params}`)
  },
  // 佐证删除
  monthlyworkplanwitnessDels (params) {
    return post('/monthlyworkplanwitness/dels', params)
  },

  // 年度
  // 管理列表
  yearWorkplanList (params) {
    return post('/yearworkplan/pcList', params)
  },
  yearWorkplanInfo (params) {
    return post(`/yearworkplan/appListInfo?id=${params}`)
  },
  yearWorkplanAddEdit (url, params) {
    return post(url, params)
  },
  yearWorkplanDels (params) {
    return post('/yearworkplan/dels', params)
  },
  // 获取启动时间的回显
  yearGetStartDate (params) {
    return post('/yearworkplan/getStartDate', params)
  },
  // 修改启动时间
  yearUpdateStartDate (params) {
    return post('/yearworkplan/updateStartDate', params)
  },
  // 佐证列表
  yearWorkplanwitnessList (params) {
    return post('/yearworkplanwitness/list', params)
  },
  // 佐证详情
  yearWorkplanwitnessInfo (params) {
    return post(`/yearworkplanwitness/info/${params}`)
  },
  // 佐证删除
  yearWorkplanwitnessDels (params) {
    return post('/yearworkplanwitness/dels', params)
  },
  // 短信模板
  yearworkplanGetSmsTGemplate (params) {
    return post('/yearworkplan/getSmsTemplate', params)
  },
  // 发送短信
  yearworkplanSmsSend (params) {
    return post('/yearworkplan/smsSend', params)
  },
  // 常态化工作列表
  normalizationWorkList (params) {
    return post('/normalizationwork/list', params)
  },
  // 常态化工作详情
  normalizationWorkInfo (params) {
    return post(`/normalizationwork/info/${params}`)
  },
  // 常态化工作删除
  normalizationWorkDels (params) {
    return post('/normalizationwork/dels', params)
  },
  // 常态化工作新增编辑
  normalizationWorkAddEdit (url, params) {
    return post(url, params)
  },
  // 月度工作计划 （2023-10-18改为周计划）
  monthlyworkscheduleList (params) {
    return post('/monthlyworkschedule/list', params)
  },
  monthlyworkscheduleInfo (params) {
    return post(`/monthlyworkschedule/info/${params}`)
  },
  monthlyworkscheduleAddEdit (url, params) {
    return post(url, params)
  },
  monthlyworkscheduleDels (params) {
    return post('/monthlyworkschedule/dels', params)
  },
  // 阶段性工作
  // 管理列表
  phasedWorkplanList (params) {
    return post('/phasework/pcList', params)
  },
  phasedWorkplanInfo (params) {
    return post(`/phasework/appListInfo?id=${params}`)
  },
  phasedWorkplanAddEdit (url, params) {
    return post(url, params)
  },
  phasedWorkplanDels (params) {
    return post('/phasework/dels', params)
  },
  // 获取启动时间的回显
  phasedGetStartDate (params) {
    return post('/phasework/getStartDate', params)
  },
  // 修改启动时间
  phasedUpdateStartDate (params) {
    return post('/phasework/updateStartDate', params)
  },
  // 佐证列表
  phasedWorkplanwitnessList (params) {
    return post('/phaseworkwitness/list', params)
  },
  // 佐证详情
  phasedWorkplanwitnessInfo (params) {
    return post(`/phaseworkwitness/info/${params}`)
  },
  // 佐证删除
  phasedWorkplanwitnessDels (params) {
    return post('/phaseworkwitness/dels', params)
  },
  // 字典年份
  getTimeSlotDict (params) {
    return post('/yearworkplan/getTimeSlotDict', params)
  },
  // 修改状态
  updateState (params) {
    return post('/yearworkplan/updateState', params)
  }
}
