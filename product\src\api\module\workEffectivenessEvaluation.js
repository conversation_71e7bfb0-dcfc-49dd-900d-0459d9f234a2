import { post } from '../http'

const workEffectivenessEvaluation = {
  dictionarypubkvs (params) {
    return post('/dictionary/pubkvs', params)
  },
  // 统计列表
  effectStatisticsList (params) {
    return post('/effectStatistics/list', params)
  },
  // 统计详情
  effectStatisticsDetail (params) {
    return post('/effectStatistics/detail', params)
  },
  // 配置管理
  effectconfigList (params) {
    return post('/effectconfig/list', params)
  },
  effectconfigAddEdit (url, params) {
    return post(url, params)
  },
  effectconfigInfo (params) {
    return post(`/effectconfig/info/${params}`)
  },
  effectconfigDels (params) {
    return post('/effectconfig/dels', params)
  },
  effectInfo (url, params) {
    return post(`/${url}/${params}`)
  },
  // 获取界别委员活动组
  getUserGroup (params) {
    return post('/effectconfig/getUserGroup', params)
  },
  // 年度工作计划管理
  effectannualworkplanList (params) {
    return post('/effectannualworkplan/list', params)
  },
  effectannualworkplanInfo (params) {
    return post(`/effectannualworkplan/info/${params}`)
  },
  effectannualworkplanDels (params) {
    return post('/effectannualworkplan/dels', params)
  },
  // 界别学习
  effectsectorlearningList (params) {
    return post('/effectsectorlearning/list', params)
  },
  effectsectorlearningInfo (params) {
    return post(`/effectsectorlearning/info/${params}`)
  },
  effectsectorlearningDels (params) {
    return post('/effectsectorlearning/dels', params)
  },
  // 界别调研
  effectsectorresearchList (params) {
    return post('/effectsectorresearch/list', params)
  },
  effectsectorresearchInfo (params) {
    return post(`/effectsectorresearch/info/${params}`)
  },
  effectsectorresearchDels (params) {
    return post('/effectsectorresearch/dels', params)
  },
  // 联系界别群众
  effectcontactuserList (params) {
    return post('/effectcontactuser/list', params)
  },
  effectcontactuserInfo (params) {
    return post(`/effectcontactuser/info/${params}`)
  },
  effectcontactuserDels (params) {
    return post('/effectcontactuser/dels', params)
  },
  // 年终述职
  effectsummarizeList (params) {
    return post('/effectsummarize/list', params)
  },
  effectsummarizeInfo (params) {
    return post(`/effectsummarize/info/${params}`)
  },
  effectsummarizeDels (params) {
    return post('/effectsummarize/dels', params)
  },
  // 调研成果
  effectresearchresultsList (params) {
    return post('/effectresearchresults/list', params)
  },
  effectresearchresultsInfo (params) {
    return post(`/effectresearchresults/info/${params}`)
  },
  effectresearchresultsDels (params) {
    return post('/effectresearchresults/dels', params)
  },
  // 会议发言
  effectmeetingspeechList (params) {
    return post('/effectmeetingspeech/list', params)
  },
  effectmeetingspeechInfo (params) {
    return post(`/effectmeetingspeech/info/${params}`)
  },
  effectmeetingspeechDels (params) {
    return post('/effectmeetingspeech/dels', params)
  },
  // 委员之家建设
  effecthomeconstructList (params) {
    return post('/effecthomeconstruct/list', params)
  },
  effecthomeconstructInfo (params) {
    return post(`/effecthomeconstruct/info/${params}`)
  },
  effecthomeconstructDels (params) {
    return post('/effecthomeconstruct/dels', params)
  },
  // 宣传工作
  effectpublicizeList (params) {
    return post('/effectpublicize/list', params)
  },
  effectpublicizeInfo (params) {
    return post(`/effectpublicize/info/${params}`)
  },
  effectpublicizeDels (params) {
    return post('/effectpublicize/dels', params)
  }
}

export default workEffectivenessEvaluation
