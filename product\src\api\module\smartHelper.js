
// 导入封装的方法
import Vue from 'vue'
import {
  post,
  get
} from '../http'
// 获取大数据接口
const getBigDataUrl = () => JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
const smartHelper = {
  // 获取推荐人
  getActivityJoinUser (params) {
    return post(`${getBigDataUrl()}/smart/activity/joinUser`, params)
  },
  // 获取推荐人
  getsurveyJoinUser (params) {
    return post(`${getBigDataUrl()}/smart/survey/joinUser`, params)
  },
  // 资讯抓取配置列表
  crawlList(params) {
    return get('/infoconfig/findInfoConfigs', params)
  },
  crawlIsEnable(params) {
    return post('/infoconfig/updateEnableByBigDataIds', params)
  },
  trigger(params) {
    return post('/infoconfig/trigger', params)
  },
  // 修改咨询配置
  editCraw(params) {
    return post('/infoconfig/updateInfoConfig', params)
  },
  // 资讯统计整体情况
  newsCountOverall(params) {
    return get(`${getBigDataUrl()}/news/nums`, params)
  },
  // 资讯统计关注点
  newsCountAggTags(params) {
    return get(`${getBigDataUrl()}/news/aggTags`, params)
  },
  // 资讯统计热词
  newsCountHotWord(params) {
    return get(`${getBigDataUrl()}/news/aggHot`, params)
  },
  // 资讯统计类型首欢迎度
  newsCountLikeRate(params) {
    return get(`${getBigDataUrl()}/news/aggPopular`, params)
  },
  // 平台用户活跃度
  newsCountActivity(params) {
    return get(`${getBigDataUrl()}/news/aggActive`, params)
  },
  // 资讯阅读数量统计
  newsCountReadNum(params) {
    return get(`${getBigDataUrl()}/news/aggByBrowser`, params)
  },
  // 意见征集排行
  optionCollectRank(params) {
    return get(`${getBigDataUrl()}/opinion/opinionRank`, params)
  },
  // 意见征集统计数据
  optionCollectCount(params) {
    return get(`${getBigDataUrl()}/opinion/opinionNum`, params)
  },
  // 意见征集月度统计数据
  optionCollectMonth(params) {
    return get(`${getBigDataUrl()}/opinion/opinionMonth`, params)
  },
  // 意见征集热词
  optionCollectHotWord(params) {
    return get(`${getBigDataUrl()}/opinion/commentHotWord`, params)
  },
  // 获取男女占比和党派分析
  MaleMembers(params) {
    return get('/partyorganization/getDataScreen', params)
  },
  // 获取组织生活
  getOrganizationalLife(params) {
    return post('/partyorganization/getOrganizationalLife?parentId=' + params)
  },
  // 反馈
  Feedback(params) {
    params.interfaceUrl = `${getBigDataUrl()}${params.url}`
    delete params.url
    return post(`${getBigDataUrl()}/smart/suggestFeedback`, params)
  }

}

export default smartHelper
