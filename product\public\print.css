.print-card .time {
  text-align: center;
  width: 100%;
  font-size: 17pt;
  font-family: 'Times New Roman';
}

.print-card .type {
  font-size: 16pt;
  font-weight: 900;
  margin-bottom: 46pt;
  font-family: '黑体';
}

.print-card .the {
  font-size: 20pt;
  font-weight: 700;
  text-align: center;
  line-height: 28pt;
  margin-bottom: 26pt;
}

.print-card .Caseno {
  font-size: 24pt;
  font-weight: 900;
  text-align: center;
  margin-bottom: 88pt;
}

.print-card .billSponsor {
  font-size: 16pt;
  font-weight: 700;
  display: flex;
  line-height: 18pt;
  margin-bottom: 18pt;
}

.print-card .billSponsorbs {
  margin-bottom: 10pt;
}

.print-card .none {
  display: none;
}

.print-card .billSponsorb {
  margin-bottom: 120pt;
}

.print-card .billSponsors {
  font-size: 16pt;
  font-weight: 400;
  line-height: 18pt;
  /* font-family: '宋体'; */
  border-bottom: 1px solid #000;
  margin-bottom: 88pt;
}

.print-card .people {
  font-weight: 400;
  width: calc(100% - 109px);
  border-bottom: 1px solid #000;
}

.print-card .flex {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 18pt;
}

.print-card .marginBottom {
  margin-bottom: 46pt;
}

.print-card .flexitem {
  width: 50%;
  display: inline-block;
  font-size: 16pt;
  line-height: 18pt;
  font-weight: 700;
  display: flex;
}

.print-card .flexitem+.flexitem {
  justify-content: flex-end;
}

.print-card .peoples {
  font-weight: 400;
  border-bottom: 1px solid #000;
}

.print-card .content {
  font-size: 15.5pt;
  line-height: 29pt;
  font-family: '宋体';
}

.print-card .tacle-ti {
  font-size: 15.5pt;
  text-align: center;
  margin-bottom: 12pt;
}

.print-card .tacle-box {
  width: 100%;
  table-layout: fixed;
  word-break: break-all;
  border-collapse: collapse;
  margin-bottom: 32pt;
}

.print-card .tavle-mb {
  text-align: center;
  line-height: 24pt;
  padding: 6pt;
  font-size: 15.5pt;
  border: 1px solid #000;
  font-family: '宋体';
}

.print-card .left {
  text-align: left;
}

/* suggestPrint */
@page {
  size: auto;
  margin: 0;
  box-sizing: border-box;
}

.suggestPrintBox {
  width: 792px;
  margin: auto;
  position: fixed;
  top: -100%;
  left: -100%;
}

.suggestPrintBox .mainModulePage {
  box-shadow: 0px 3px 15px black;
  margin: 22px 0;
  padding: 88px 88px;
}

.suggestPrintBox .mainModulePageTable {
  width: 100%;
  table-layout: fixed;
  word-break: break-all;
  border-collapse: collapse;
  margin-bottom: 28px;
}

.suggestPrintBox .mainModulePageTableTd {
  text-align: center;
  line-height: 24px;
  padding: 8px;
  font-size: 21px;
  border: 1px solid #000;
  font-family: "宋体";
  display: table-cell;
  vertical-align: middle;
}

.suggestPrint {
  width: 100%;
  height: 100%;
}

.suggestPrint .mainModulePage {
  width: 100%;
  min-height: 1120px;
  max-height: 1120px;
  padding: 88px 88px;
  position: relative;
}

.suggestPrint .mainModulePage .mainModulePageType {
  font-size: 22px;
  font-weight: bold;
  padding-bottom: 68px;
  display: flex;
  justify-content: space-between;
}

.suggestPrint .mainModulePage .mainModulePageType span {
  color: red;
}

.suggestPrint .mainModulePage .mainModulePageSlogan {
  color: red;
  font-size: 26px;
  font-weight: bold;
  text-align: center;
  padding-bottom: 38px;
}

.suggestPrint .mainModulePage .mainModulePageNumber {
  color: red;
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  padding-bottom: 88px;
}

.suggestPrint .mainModulePage .mainModulePageItem {
  width: 100%;
  display: flex;
  font-size: 22px;
  line-height: 26px;
  padding-bottom: 22px;
}

.suggestPrint .mainModulePage .mainModulePageItem span {
  display: inline-block;
}

.suggestPrint .mainModulePage .mainModulePageItem .mainModulePageItemTitle {
  width: 92px;
  color: red;
  font-weight: bold;
  text-align: justify;
  text-align-last: justify;
}

.suggestPrint .mainModulePage .mainModulePageItem .mainModulePageItemColon {
  width: 16px;
  color: red;
}

.suggestPrint .mainModulePage .mainModulePageItem .mainModulePageItemContent {
  width: calc(100% - 108px);
  border-bottom: 1px solid #262626;
}

.suggestPrint .mainModulePage .mainModulePageItemB {
  padding-bottom: 66px;
}

.suggestPrint .mainModulePage .mainModulePageItemTwo {
  width: 100%;
  height: 27px;
  font-size: 22px;
  min-height: 27px;
  line-height: 26px;
  border-bottom: 1px solid #262626;
}

.suggestPrint .mainModulePage .mainModulePageItemFlex {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.suggestPrint .mainModulePage .mainModulePageItemFlex .mainModulePageItem {
  width: 46%;
}

.suggestPrint .mainModulePage .mainModulePageItemTime {
  color: red;
  text-align: center;
  font-size: 23px;
  padding-top: 120px;
  padding-bottom: 22px;
  font-family: "Times New Roman";
}

.suggestPrint .mainModulePage .pageNumber {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  text-align: right;
  font-size: 14px;
  padding-right: 32px;
  padding-bottom: 22px;
}

.suggestPrint .mainModulePage .mainModulePageTableName {
  font-size: 21px;
  text-align: center;
  margin-bottom: 16px;
}

.suggestPrint .mainModulePage .mainModulePageTable {
  width: 100%;
  table-layout: fixed;
  word-break: break-all;
  border-collapse: collapse;
  margin-bottom: 41px;
}

.suggestPrint .mainModulePage .TableMargin {
  margin-bottom: 0;
}

.suggestPrint .mainModulePage .mainModulePageTableTd {
  text-align: center;
  line-height: 24px;
  padding: 8px;
  font-size: 21px;
  border: 1px solid #000;
  font-family: "宋体";
  display: table-cell;
  vertical-align: middle;
}

.suggestPrint .mainModulePage .padding {
  padding: 20px 8px;
}

.suggestPrint .mainModulePage .mainModulePageContent {
  font-size: 21px;
  min-height: 36px;
  line-height: 36px;
  font-family: "宋体";
}

.suggestPrint .mainModulePage .center {
  text-align: center;
}

.suggestPrint .mainModulePage .right {
  text-align: right;
}

/* ！suggestPrint */


/* suggestPrintOne */
.suggestDetails {
  margin: auto;
  padding-bottom: 24px;
}

.suggestDetails .details-title {
  position: relative;
}

.suggestDetails .details-title .detailsTitleName {
  padding-bottom: 16px;
}

.suggestDetails .details-title .printIcon {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 38px;
  height: 38px;
  background: url("../../../../assets/img/dy.png");
  background-size: 100% 100%;
  cursor: pointer;
}

.suggestDetails .similarTitle {
  width: calc(100% - 120px);
  margin-right: 22px;
}

.suggestDetails .similarTitleButton {
  height: 32px;
  padding: 0 10px;
  margin-left: 0;
}

.suggestDetails .details-item-content-label {
  position: relative;
}

.suggestDetails .details-item-content-label .contentButton {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}

.suggestDetails .el-tag+.el-tag {
  margin-left: 12px;
}

.suggestDetails .details-item-unit {
  padding: 9px;
}

.suggestDetails .situation {
  padding: 6px 0;
}

.suggestDetails .situation .el-radio__input.is-disabled.is-checked .el-radio__inner {
  background-color: #fff;
  border-color: #BC1D1D;
}

.suggestDetails .situation .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
  background-color: #BC1D1D;
}

.suggestDetails .situation .el-radio__input.is-disabled+span.el-radio__label {
  color: #262626;
}

.suggestDetails .classification {
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: 16px;
}

.suggestDetails .classification .el-select+.el-select {
  margin-left: 16px;
}

.suggestDetails .classificationRadio {
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: 16px;
}

.suggestDetails .classificationRadio .el-radio {
  width: 100%;
  margin-right: 0;
  padding: 6px 0;
}

.suggestDetails .ReviewDetailsText {
  width: 520px;
  height: 360px;
  padding: 24px;
  line-height: 22px;
}

.suggestDetails .classificationinput {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
}

.suggestDetails .classificationinput .el-input {
  width: 100%;
  border-color: transparent;
}

.suggestDetails .classificationinput .el-input .el-input__inner {
  border-color: transparent;
}

.suggestDetails .form-unit-box {
  width: 100%;
  min-height: 40px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  padding: 0 16px;
  padding-right: 40px;
  padding-top: 6px;
}

.suggestDetails .form-unit-box .form-user-box-text {
  color: #999;
  font-size: 14px;
  padding-bottom: 6px;
  line-height: 28px;
}

.suggestDetails .form-unit-box .el-tag {
  margin-bottom: 8px;
  margin-right: 12px;
  margin-left: 0;
}

.suggestDetails .form-unit-box:hover {
  border-color: #BC1D1D;
}

.suggestDetails .form-unit-box:focus {
  border-color: #BC1D1D;
}

.suggestDetails .addreplybox .el-button {
  display: inline-block !important;
}

.suggestDetails .addreplybox .addreply {
  margin-left: 28px !important;
}

.suggestDetails .Adjust {
  position: relative;
}

.suggestDetails .Adjust .el-button {
  position: absolute;
  right: 22px;
  height: 32px;
  padding: 0 12px;
}

.suggestDetails .detailsVlaue {
  padding: 6px 12px;
  padding-right: 3px;
  line-height: 22px;
}

/*  ！suggestPrintOne */

/* 基础样式 */

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
main,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
}

@media print {
  body {
    -webkit-print-color-adjust: exact;
  }
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

html,
body {
  font-family: Microsoft YaHei;
}

.button-box {
  min-height: 64px;
  display: flex;
  flex-wrap: wrap;
  padding-left: 24px;
  padding-top: 12px;
}

.button-box .el-button {
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  margin-right: 12px;
  margin-bottom: 12px;
}

.button-box .el-button .el-button [class*='el-icon-']+span {
  margin-left: 9px;
}

.button-box .el-button+.el-button {
  margin-left: 0;
}

.paging_box {
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.newForm {
  padding: 24px 30px 24px 40px;
}

.newForm .form-candidates {
  width: 100%;
}

.newForm .form-candidates .el-form-item__content {
  height: auto;
  line-height: auto;
}

.newForm .form-candidates .el-form-item__content .candidates-button {
  height: 100%;
  display: flex;
  justify-content: space-between;
}

.newForm .form-candidates .el-form-item__content .candidates-button .el-button {
  height: 44px;
  width: 104px;
}

.newForm .form-candidates .el-form-item__content .candidates-button .candidates-number {
  height: 44px;
  line-height: 44px;
  display: flex;
  align-items: center;
}

.newForm .form-candidates .el-form-item__content .candidates-button .candidates-number .number {
  font-size: 14px;
}

.newForm .form-candidates .el-form-item__content .candidates-button .candidates-number .all-candidates {
  margin-left: 6px;
  font-size: 12px;
  color: #8c8c8c;
}

.newForm .form-candidates .el-form-item__content .candidates-button .candidates-number .all-candidates .el-icon-arrow-down {
  margin-left: 4px;
  transform: rotate(0deg);
  transition-duration: 0.2s;
}

.newForm .form-candidates .el-form-item__content .candidates-button .candidates-number .all-candidates-active {
  color: #0473de;
}

.newForm .form-candidates .el-form-item__content .candidates-button .candidates-number .all-candidates-active .el-icon-arrow-down {
  transform: rotate(-180deg);
  transition-duration: 0.2s;
}

.newForm .candidates {
  width: calc(100% - 24px);
  padding: 8px;
  border: 1px solid #d9d9d9;
  box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  height: 116px;
  max-height: 116px;
  overflow: hidden;
}

.newForm .candidates .candidates-box {
  width: 94px;
  min-width: 94px;
  max-width: 94px;
  height: 28px;
  line-height: 28px;
  padding: 0 9px;
  background: rgba(4, 115, 222, 0.1);
  border-radius: 2px;
  display: flex;
  align-items: center;
  margin-right: 8px;
  margin-bottom: 8px;
}

.newForm .candidates .candidates-box .candidates-text {
  font-size: 14px;
  width: 100%;
  text-align: center;
}

.newForm .candidates .candidates-box .candidates-close {
  margin-left: 6px;
  width: 14px;
  min-width: 14px;
  height: 14px;
  background: url("../socialImg/close.png");
  background-size: 100% 100%;
}

.newForm .candidates-active {
  box-sizing: border-box;
  overflow: hidden;
  height: auto;
  max-height: 100%;
  transition: max-height 1s;
}

.newForm .el-form-item .el-form-item__label {
  padding: 12px 0;
  padding-top: 6px;
  line-height: 24px;
  color: #222222;
}

.newForm .form-title {
  width: 100%;
  margin-right: 0;
  padding-right: 10px;
}

.newForm .form-title .el-input {
  width: 100%;
}

.newForm .form-input {
  width: 296px;
}

.newForm .form-input .el-input {
  width: 296px;
}

.newForm .form-input .el-input-number {
  width: 100%;
}

.newForm .zy-tree-select {
  width: 296px;
}

.newForm .form-multiple-input {
  width: 100%;
}

.newForm .form-multiple-input .el-input {
  width: 296px;
}

.newForm .form-multiple-input .el-input+.el-input {
  margin-left: 24px;
}

.newForm .form-multiple {
  width: 100%;
}

.newForm .form-multiple .el-input {
  width: 296px;
  margin-left: 30px;
}

.newForm .form-ue {
  width: 100%;
  margin-right: 0;
  padding-right: 10px;
}

.newForm .form-ue .el-form-item__content {
  line-height: normal;
}

.newForm .form-upload {
  width: 100%;
  margin-right: 0;
  padding-right: 10px;
}

.newForm .form-upload .form-upload-demo .el-upload {
  width: 100%;
}

.newForm .form-upload .form-upload-demo .el-upload .el-upload-dragger {
  height: auto;
  min-height: 79px;
  width: 100%;
  max-width: 952px;
  background-color: #e6e5e8;
}

.newForm .form-upload .form-upload-demo .el-upload .el-upload-dragger .el-upload__text {
  padding-top: 22px;
  font-size: 14px;
  line-height: 22px;
  padding-bottom: 12px;
}

.newForm .form-upload .form-upload-demo .el-upload .el-upload-dragger .el-upload__tip {
  font-size: 12px;
  line-height: 20px;
  color: #6e6e6e;
  margin-top: 0;
  padding: 0 6%;
  padding-bottom: 16px;
}

.newForm .form-icon {
  width: 296px;
}

.newForm .form-icon .form-icon-uploader {
  width: 128px;
  height: 128px;
  border: 1px dashed #ccc;
}

.newForm .form-icon .form-icon-uploader:hover {
  border-color: #BC1D1D;
}

.newForm .form-icon .form-icon-uploader .user-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 128px;
  height: 128px;
  line-height: 128px;
  text-align: center;
}

.newForm .form-icon .form-icon-uploader .form-icon-img {
  width: 128px;
  height: 128px;
  position: relative;
}

.newForm .form-icon .form-icon-uploader .form-icon-img img {
  width: 100%;
  height: 100%;
  display: block;
}

.newForm .form-icon .form-icon-uploader .form-icon-img .form-icon-img-del {
  position: absolute;
  top: 0;
  right: 0;
  width: 22px;
  height: 22px;
  transform: translate(50%, -50%);
  background: url("../../assets/img/close.png") no-repeat;
  background-size: 100% 100%;
}

.newForm .form-button {
  display: flex;
  justify-content: center;
  padding: 6px 0;
  padding-right: 10px;
}

.newForm .form-button .el-button {
  height: 36px;
  line-height: 36px;
  padding: 0 16px;
}

.newForm .form-button .el-button+.el-button {
  margin-left: 24px;
}

.details {
  width: 960px;
  padding: 0 !important;
  padding-bottom: 24px !important;
}

.details .details-content {
  line-height: 26px;
}

.details .details-title {
  text-align: center;
  font-size: 28px;
  line-height: 38px;
  padding: 32px 52px;
  font-weight: bold;
  color: #000;
}

.details .details-title .details-title-c {
  color: #ff0000;
}

.details .details-item-title {
  width: 100%;
  min-height: 68px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  box-sizing: border-box;
}

.details .details-item-title .details-item-label {
  width: 148px;
  display: flex;
  font-weight: 500;
  align-items: center;
  border-right: 1px solid #e6e6e6;
  padding-right: 9px;
  justify-content: center;
  color: #262626;
  background-color: #f8faff;
}

.details .details-item-title .details-item-value {
  display: flex;
  align-items: center;
  padding: 9px;
  width: calc(100% - 148px);
  line-height: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.details .details-item-box {
  width: 100%;
  border-top: 1px solid #e6e6e6;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
}

.details .details-item-box .details-item-column {
  width: 50%;
  flex-direction: column;
}

.details .details-item-box .details-item-column+.details-item-column {
  border-left: 1px solid #e6e6e6;
}

.details .details-item-box .details-item-img {
  height: 215px;
  width: 100%;
  border-bottom: 1px solid #e6e6e6;
  min-height: 43px;
  display: flex;
  box-sizing: border-box;
}

.details .details-item-box .details-item-img .details-item-label {
  width: 148px;
  height: 100%;
  display: flex;
  font-weight: 500;
  align-items: center;
  border-right: 1px solid #e6e6e6;
  padding-right: 9px;
  justify-content: center;
  color: #262626;
  background-color: #f8faff;
}

.details .details-item-box .details-item-img .details-item-value {
  width: calc(100% - 148px);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
}

.details .details-item-box .details-item-img .details-item-value img {
  height: 200px;
}

.details .details-item-box .details-item {
  width: 100%;
  min-height: 43px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  box-sizing: border-box;
}

.details .details-item-box .details-item .details-item-label {
  width: 148px;
  display: flex;
  font-weight: 500;
  align-items: center;
  border-right: 1px solid #e6e6e6;
  padding-right: 9px;
  justify-content: center;
  color: #262626;
  background-color: #f8faff;
}

.details .details-item-box .details-item .details-item-value {
  padding: 9px;
  width: calc(100% - 148px);
  line-height: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.details .details-item-box .details-item .details-item-value .details-item-file {
  cursor: pointer;
}

.details .details-item-box .details-item .details-item-value .details-item-file span {
  margin-left: 16px;
  color: #BC1D1D;
}

.details .details-item-box .details-item .replybutton .el-button--text {
  padding: 6px 0;
  display: block;
}

.details .details-item-box .details-item .replybutton .el-button+.el-button {
  margin: 0;
}

.details .details-item-box .details-item .detailsInfoBox {
  padding: 0;
}

.details .details-item-box .details-item .detailsInfoBox .el-button--text {
  padding: 6px 0;
  display: block;
}

.details .details-item-box .details-item .detailsInfoBox .el-button+.el-button {
  margin: 0;
}

.details .details-item-box .details-item .detailsInfoBox .detailsInfo {
  padding: 9px;
  font-size: 14px;
}

.details .details-item-box .details-item .detailsInfoBox .detailsInfo+.detailsInfo {
  border-top: 1px solid #e6e6e6;
}

.details .details-item-box .details-item .detailsInfoBox .detailsInfoText {
  width: 100%;
  padding: 0 9px;
  height: 43px;
  display: flex;
  justify-content: space-between;
}

.details .details-item-box .details-item .detailsInfoBox .detailsInfoText .el-button--text {
  padding: 2px 0;
}

.details .details-item-box .details-item .detailsInfoBox .detailsInfoText .details-item-value-button {
  padding: 0 9px;
  width: 199px;
  border-left: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
}

.details .details-item-box .details-item .detailsInfoBox .detailsInfoText .details-item-value-button .el-button {
  height: 32px;
  line-height: 31px;
  padding: 0 16px;
}

.details .details-item-box .details-item-content-label {
  width: 100%;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e6e6e6;
  padding-right: 9px;
  justify-content: center;
  color: #262626;
  padding: 9px;
  background-color: #f8faff;
  line-height: 24px;
  text-align: center;
}

.details .details-item-box .details-item-content {
  width: 100%;
  padding: 40px;
  /* border-bottom: 1px solid #e6e6e6; */
  overflow: hidden;
  line-height: 2;
}

.details .details-item-box .details-item-content img,
.details .details-item-box .details-item-content video {
  max-width: 100%;
}

.details .details-item-box .details-contact {
  width: 100%;
}

.details .details-item-box .details-contact .details-item-contact-label {
  width: 100%;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e6e6e6;
  padding-right: 9px;
  justify-content: center;
  color: #262626;
  padding: 9px;
  background-color: #e9effe;
  line-height: 24px;
  text-align: center;
}

.details .details-item-box .details-contact .details-contact-item {
  display: flex;
  width: 100%;
  min-height: 43px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  box-sizing: border-box;
}

.details .details-item-box .details-contact .details-contact-item .label {
  background-color: #f8faff;
}

.details .details-item-box .details-contact .details-contact-item .label .span {
  color: #d90005;
}

.details .details-item-box .details-contact .details-contact-item .details-contact-item-value {
  width: 148px;
  display: flex;
  font-weight: 500;
  align-items: center;
  border-right: 1px solid #e6e6e6;
  padding-right: 9px;
  justify-content: center;
  color: #262626;
}

.details .details-item-box .details-contact .details-contact-item .details-contact-item-value-box {
  width: calc(100% - 148px);
  display: flex;
}

.details .details-item-box .details-contact .details-contact-item .row1 {
  flex: 1;
}

.details .details-item-box .details-contact .details-contact-item .row2 {
  flex: 2;
}

.details .details-item-box .details-contact .details-contact-item .row3 {
  flex: 3;
}

.details .details-item-box .details-contact .details-contact-item .row4 {
  flex: 4;
}

.details .details-item-box .details-contact .details-contact-item .row5 {
  flex: 5;
}

.details .details-item-box .details-button {
  padding-top: 24px;
  padding-left: 24px;
}

.newFormPaper {
  padding: 0 52px;
  box-shadow: 0px 3px 15px rgba(0, 0, 0, 0.16);
}

.newFormPaper .title {
  padding: 52px 0;
  border-bottom: 5px solid #BC1D1D;
  font-size: 26px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333333;
  letter-spacing: 3px;
  text-align: center;
}

.newFormPaper .title span {
  color: red;
}

.newFormPaper .el-form-item {
  margin: 0;
  border-bottom: 1px solid #BC1D1D;
  min-height: 42px;
}

.newFormPaper .el-form-item .el-form-item__label {
  text-align: center;
  padding: 0;
}

.newFormPaper .el-form-item .el-form-item__content {
  width: calc(100% - 136px);
  min-height: 41px;
  border-left: 1px solid #BC1D1D;
}

.newFormPaper .el-form-item .el-form-item__content .el-input__inner {
  border: 0;
}

.newFormPaper .el-form-item .el-form-item__content .el-radio-group {
  padding-left: 12px;
}

.newFormPaper .formTitle {
  width: 100%;
}

.newFormPaper .formTitle .el-button {
  height: 32px;
  padding: 0 22px;
  margin-left: 12px;
}

.newFormPaper .linkman .linkmanHead {
  display: flex;
  min-height: 42px;
}

.newFormPaper .linkman .linkmanHead .linkmanItem {
  line-height: 41px;
}

.newFormPaper .linkman .linkmanBody {
  display: flex;
  min-height: 42px;
}

.newFormPaper .linkman .linkmanBody .linkmanItem {
  border-bottom: 0;
}

.newFormPaper .linkman .linkmanBody+.linkmanBody .linkmanItem {
  border-bottom: 0;
  border-top: 1px solid #BC1D1D;
}

.newFormPaper .linkman .row1 {
  flex: 1;
}

.newFormPaper .linkman .row2 {
  flex: 2;
}

.newFormPaper .linkman .row3 {
  flex: 3;
}

.newFormPaper .linkman .linkmanItem {
  min-height: 42px;
  text-align: center;
  border-bottom: 1px solid #BC1D1D;
}

.newFormPaper .linkman .linkmanItem .el-link {
  font-size: 18px;
  line-height: 24px;
}

.newFormPaper .linkman .linkmanItem .el-link+.el-link {
  margin-left: 12px;
}

.newFormPaper .linkman .linkmanItem+.linkmanItem {
  border-left: 1px solid #BC1D1D;
}

.newFormPaper .titleButton .el-input {
  width: calc(100% - 136px);
}

.newFormPaper .formInput {
  width: 50%;
}

.newFormPaper .formInput .el-select {
  width: 100%;
}

.newFormPaper .formInput .zy-tree-select {
  width: 100%;
}

.newFormPaper .formInput+.formInput {
  border-left: 1px solid #BC1D1D;
}

.newFormPaper .formUpload {
  width: 100%;
}

.newFormPaper .formUpload .form-upload-demo {
  padding: 12px;
}

.newFormPaper .formUpload .form-upload-demo .el-upload {
  width: 100%;
}

.newFormPaper .formUpload .form-upload-demo .el-upload .el-upload-dragger {
  height: 79px;
  width: 100%;
  max-width: 952px;
  background-color: #e6e5e8;
}

.newFormPaper .formUpload .form-upload-demo .el-upload .el-upload-dragger .el-upload__text {
  padding-top: 22px;
  font-size: 14px;
  line-height: 22px;
}

.newFormPaper .formUpload .form-upload-demo .el-upload .el-upload-dragger .el-upload__tip {
  font-size: 12px;
  line-height: 20px;
  color: #6e6e6e;
  margin-top: 0;
}

.newFormPaper .contentUe {
  padding-bottom: 12px;
  border-bottom: 1px solid #BC1D1D;
}

.newFormPaper .form-situation {
  width: 100%;
}

.newFormPaper .form-situation .el-form-item__content {
  padding-left: 12px;
}

.newFormPaper .form-situation .form-situation-box {
  width: 100%;
  display: flex;
}

.newFormPaper .form-situation .form-situation-box .red {
  color: #d90005;
}

.newFormPaper .form-situation .form-situation-box .form-situation-box-radio {
  padding: 0 12px;
}

.newFormPaper .form-unit-box {
  width: 100%;
  min-height: 40px;
  background-color: #fff;
  display: flex;
  flex-wrap: wrap;
  padding: 0 16px;
  padding-right: 40px;
  padding-top: 6px;
}

.newFormPaper .form-unit-box .form-user-box-text {
  color: #999;
  font-size: 14px;
  padding-bottom: 6px;
  line-height: 28px;
}

.newFormPaper .form-unit-box .el-tag {
  margin-bottom: 6px;
  margin-right: 12px;
}

.newFormPaper .formButton {
  padding: 52px 0;
  padding-top: 32px;
  display: flex;
  justify-content: center;
}

.newFormPaper .formButton .el-button {
  width: 102px;
  height: 36px;
  padding: 0 22px;
  margin-left: 12px;
}

.tableSmall {
  width: 100%;
}

.tableSmall .el-table th {
  padding: 0 !important;
  height: 42px !important;
}

.tableSmall .el-table__body td {
  height: 42px !important;
}

.tableSmall .el-scrollbar__wrap {
  margin-right: 0 !important;
}

.unitTabBox {
  width: 100%;
  display: flex;
  padding: 12px 0;
}

.unitTabBox .unitTabItem {
  height: 38px;
  padding: 0 18px;
  color: #BC1D1D;
  line-height: 38px;
  margin-left: 12px;
  background-color: rgba(188, 29, 29, 0.1);
  position: relative;
  cursor: pointer;
  border-radius: 4px;
}

.unitTabBox .unitTabItem span {
  display: inline-block;
  height: 16px;
  line-height: 16px;
  width: 42px;
  border-radius: 9px;
  text-align: center;
  margin-left: 6px;
  border: 1px solid #BC1D1D;
}

.unitTabBox .unitTabItem::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translate(-50%, 100%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid transparent;
}

.unitTabBox .unitTabItem+.unitTabItem {
  margin-left: 22px;
}

.unitTabBox .unitTabItemA {
  color: #fff !important;
  background-color: #BC1D1D !important;
}

.unitTabBox .unitTabItemA span {
  color: #BC1D1D;
  background-color: #fff;
  border: 1px solid #fff;
}

.unitTabBox .unitTabItemA::after {
  border-top: 8px solid #BC1D1D;
}

.tableData {
  width: 100%;
}

.tableData .tableDataFileList {
  width: 100%;
}

.tableData .tableDataFileList .tableDataFileItem {
  height: 40px;
  padding: 8px 0;
}

.tableData .tableDataFileList .tableDataFileItem .tableDataFile {
  max-width: 100%;
  display: inline-block;
  padding-right: 88px;
  line-height: 24px;
  position: relative;
}

.tableData .tableDataFileList .tableDataFileItem .tableDataFile span {
  line-height: 24px;
  position: absolute;
  top: 0;
  right: 48px;
  color: #BC1D1D;
}

.tableData .tableDataFileList .tableDataFileItem .tableDataFile span+span {
  right: 0;
}

/* ！ 基础样式 */

/* one */
@page {
  size: auto;
  margin: 0;
  box-sizing: border-box;
}

.proposalPrintOneBox {
  width: 792px;
  margin: auto;
  position: fixed;
  top: -100%;
  left: -100%;
}

.proposalPrintOneBox .mainModulePage {
  box-shadow: 0px 3px 15px black;
  margin: 22px 0;
  padding: 88px 88px;
}

.proposalPrintOneBox .mainModulePageTable {
  width: 100%;
  table-layout: fixed;
  word-break: break-all;
  border-collapse: collapse;
  margin-bottom: 28px;
}

.proposalPrintOneBox .mainModulePageTableTd {
  text-align: center;
  line-height: 24px;
  padding: 8px;
  border: 1px solid #000;
  font-family: "宋体";
  display: table-cell;
  vertical-align: middle;
}

.proposalPrintOne {
  width: 100%;
  height: 100%;
}

.proposalPrintOne .mainModulePage {
  width: 100%;
  min-height: 1120px;
  max-height: 1120px;
  padding: 88px 88px;
  position: relative;
}

.proposalPrintOne .mainModulePage .mainModulePageType {
  display: flex;
  justify-content: flex-end;
  font-size: 14px;
}

.proposalPrintOne .mainModulePage .mainModulePageType div {
  color: #b4e1ff;
}

.proposalPrintOne .mainModulePage .mainModulePageType div span {
  display: inline-block;
  padding: 5px 12px;
  border-bottom: 1px solid #b4e1ff;
  color: #262626;
}

.proposalPrintOne .mainModulePage .mainModulePageSlogan {
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  padding: 32px 0;
}

.proposalPrintOne .mainModulePage .mainModulePageSlogan span {
  color: red;
}

.proposalPrintOne .mainModulePage .mainModulePageNumber {
  font-size: 26px;
  font-weight: bold;
  text-align: center;
  padding-bottom: 38px;
}

.proposalPrintOne .mainModulePage .mainModulePageNumber span {
  color: red;
}

.proposalPrintOne .mainModulePage .mainModulePageItem {
  width: 100%;
  display: flex;
  align-items: center;
  border-top: 1px solid #b4e1ff;
  font-size: 14px;
}

.proposalPrintOne .mainModulePage .mainModulePageItem .mainModulePageLabel {
  width: 120px;
  height: 100%;
  padding: 0 2px;
  line-height: 18px;
  text-align: center;
}

.proposalPrintOne .mainModulePage .mainModulePageItem .mainModulePageValue {
  width: calc(100% - 120px);
  min-height: 40px;
  line-height: 18px;
  padding: 11px;
  border-left: 1px solid #b4e1ff;
}

.proposalPrintOne .mainModulePage .mainModulePageItem .title {
  min-height: 58px;
  display: flex;
  align-items: center;
}

.proposalPrintOne .mainModulePage .mainModulePageItem .Relevant {
  padding: 0;
}

.proposalPrintOne .mainModulePage .mainModulePageItem .Relevant .mainModulePageItem {
  border-top: 0px solid #b4e1ff;
}

.proposalPrintOne .mainModulePage .mainModulePageItem .Relevant .mainModulePageItem .mainModulePageLabel {
  width: 50%;
}

.proposalPrintOne .mainModulePage .mainModulePageItem .Relevant .mainModulePageItem .mainModulePageValue {
  width: 50%;
}

.proposalPrintOne .mainModulePage .mainModulePageItem .Relevant .mainModulePageItem+.mainModulePageItem {
  border-top: 1px solid #b4e1ff;
}

.proposalPrintOne .mainModulePage .mainModulePagecolumn {
  width: 50%;
  display: inline-block;
}

.proposalPrintOne .mainModulePage .mainModulePagecolumn+.mainModulePagecolumn {
  border-left: 1px solid #b4e1ff;
}

.proposalPrintOne .mainModulePage .mainModulePageContentLabel {
  font-size: 14px;
  line-height: 40px;
  text-align: center;
  border-top: 1px solid #b4e1ff;
  border-bottom: 1px solid #b4e1ff;
}

.proposalPrintOne .mainModulePage .pageNumber {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  text-align: right;
  font-size: 14px;
  padding-right: 32px;
  padding-bottom: 22px;
}

.proposalPrintOne .mainModulePage .mainModulePageTableName {
  font-size: 18px;
  text-align: center;
  margin-bottom: 16px;
}

.proposalPrintOne .mainModulePage .mainModulePageTable {
  width: 100%;
  table-layout: fixed;
  word-break: break-all;
  border-collapse: collapse;
  margin-bottom: 41px;
}

.proposalPrintOne .mainModulePage .TableMargin {
  margin-bottom: 0;
}

.proposalPrintOne .mainModulePage .mainModulePageTableTd {
  text-align: center;
  line-height: 24px;
  padding: 8px;
  border: 1px solid #000;
  font-family: "宋体";
  display: table-cell;
  vertical-align: middle;
}

.proposalPrintOne .mainModulePage .padding {
  padding: 20px 8px;
}

.proposalPrintOne .mainModulePage .mainModulePageContent {
  min-height: 32px;
  line-height: 32px;
  font-family: "宋体";
}

.proposalPrintOne .mainModulePage .center {
  text-align: center;
}

.proposalPrintOne .mainModulePage .right {
  text-align: right;
}

/* ! one */

/* two */
.proposalTemplateTwo {
  width: 100%;
  height: 100%;
}

.proposalTemplateTwo .mainModulePage {
  width: 792px;
  height: 1120px;
  min-height: 1120px;
  max-height: 1120px;
  padding: 88px 88px;
  margin: 22px;
  box-shadow: 0px 3px 15px black;
  position: relative;
}

.proposalTemplateTwo .mainModulePage .mainModulePageType {
  display: flex;
  justify-content: flex-end;
  font-size: 14px;
}

.proposalTemplateTwo .mainModulePage .mainModulePageType div {
  color: #b4e1ff;
}

.proposalTemplateTwo .mainModulePage .mainModulePageType div span {
  display: inline-block;
  padding: 5px 12px;
  border-bottom: 1px solid #b4e1ff;
  color: #262626;
}

.proposalTemplateTwo .mainModulePage .mainModulePageSlogan {
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  padding: 32px 0;
}

.proposalTemplateTwo .mainModulePage .mainModulePageSlogan span {
  color: red;
}

.proposalTemplateTwo .mainModulePage .mainModulePageNumber {
  font-size: 26px;
  font-weight: bold;
  text-align: center;
  padding-bottom: 38px;
}

.proposalTemplateTwo .mainModulePage .mainModulePageNumber span {
  color: red;
}

.proposalTemplateTwo .mainModulePage .mainModulePageItem {
  width: 100%;
  display: flex;
  align-items: center;
  border-top: 1px solid #b4e1ff;
  font-size: 14px;
}

.proposalTemplateTwo .mainModulePage .mainModulePageItem .mainModulePageLabel {
  width: 120px;
  height: 100%;
  padding: 0 2px;
  line-height: 18px;
  text-align: center;
}

.proposalTemplateTwo .mainModulePage .mainModulePageItem .mainModulePageValue {
  width: calc(100% - 120px);
  min-height: 40px;
  line-height: 18px;
  padding: 11px;
  border-left: 1px solid #b4e1ff;
}

.proposalTemplateTwo .mainModulePage .mainModulePageItem .title {
  min-height: 58px;
  display: flex;
  align-items: center;
}

.proposalTemplateTwo .mainModulePage .mainModulePageItem .Relevant {
  padding: 0;
}

.proposalTemplateTwo .mainModulePage .mainModulePageItem .Relevant .mainModulePageItem {
  border-top: 0px solid #b4e1ff;
}

.proposalTemplateTwo .mainModulePage .mainModulePageItem .Relevant .mainModulePageItem .mainModulePageLabel {
  width: 50%;
}

.proposalTemplateTwo .mainModulePage .mainModulePageItem .Relevant .mainModulePageItem .mainModulePageValue {
  width: 50%;
}

.proposalTemplateTwo .mainModulePage .mainModulePageItem .Relevant .mainModulePageItem+.mainModulePageItem {
  border-top: 1px solid #b4e1ff;
}

.proposalTemplateTwo .mainModulePage .mainModulePagecolumn {
  width: 50%;
  display: inline-block;
}

.proposalTemplateTwo .mainModulePage .mainModulePagecolumn+.mainModulePagecolumn {
  border-left: 1px solid #b4e1ff;
}

.proposalTemplateTwo .mainModulePage .mainModulePageContentLabel {
  font-size: 14px;
  line-height: 40px;
  text-align: center;
  border-top: 1px solid #b4e1ff;
  border-bottom: 1px solid #b4e1ff;
}

.proposalTemplateTwo .mainModulePage .pageNumber {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  text-align: right;
  font-size: 14px;
  padding-right: 32px;
  padding-bottom: 22px;
}

.proposalTemplateTwo .mainModulePage .mainModulePageTableName {
  font-size: 18px;
  text-align: center;
  margin-bottom: 16px;
}

.proposalTemplateTwo .mainModulePage .mainModulePageTable {
  width: 100%;
  table-layout: fixed;
  word-break: break-all;
  border-collapse: collapse;
  margin-bottom: 41px;
}

.proposalTemplateTwo .mainModulePage .TableMargin {
  margin-bottom: 0;
}

.proposalTemplateTwo .mainModulePage .mainModulePageTableTd {
  text-align: center;
  line-height: 24px;
  padding: 8px;
  border: 1px solid #000;
  font-family: "宋体";
  display: table-cell;
  vertical-align: middle;
}

.proposalTemplateTwo .mainModulePage .padding {
  padding: 20px 8px;
}

.proposalTemplateTwo .mainModulePage .mainModulePageContent {
  min-height: 32px;
  line-height: 32px;
  font-family: "宋体";
}

.proposalTemplateTwo .mainModulePage .center {
  text-align: center;
}

.proposalTemplateTwo .mainModulePage .right {
  text-align: right;
}

/* two */

/* .suggestPrintBoxOne */

@charset "UTF-8";

.suggestPrintOneBox {
  width: 792px;
  margin: auto;
  position: fixed;
  top: -100%;
  left: -100%;
}

.suggestPrintOneBox .mainModulePage {
  box-shadow: 0px 3px 15px black;
  margin: 22px 0;
  padding: 88px 88px;
}

.suggestPrintOneBox .mainModulePageTable {
  width: 100%;
  table-layout: fixed;
  word-break: break-all;
  border-collapse: collapse;
  margin-bottom: 28px;
}

.suggestPrintOneBox .mainModulePageTableTd {
  text-align: center;
  line-height: 24px;
  padding: 8px;
  border: 1px solid #000;
  font-family: "宋体";
  display: table-cell;
  vertical-align: middle;
}

.suggestPrintOne {
  width: 100%;
  height: 100%;
}

.suggestPrintOne .mainModulePage {
  width: 100%;
  min-height: 1120px;
  max-height: 1120px;
  padding: 88px 88px;
  position: relative;
}

.suggestPrintOne .mainModulePage .mainModulePageType {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.suggestPrintOne .mainModulePage .mainModulePageType div {
  color: #b4e1ff;
}

.suggestPrintOne .mainModulePage .mainModulePageType div span {
  display: inline-block;
  padding: 5px 12px;
  border-bottom: 1px solid #b4e1ff;
  color: #262626;
}

.suggestPrintOne .mainModulePage .mainModulePageSlogan {
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  padding: 32px 0;
}

.suggestPrintOne .mainModulePage .mainModulePageSlogan span {
  color: red;
}

.suggestPrintOne .mainModulePage .mainModulePageNumber {
  font-size: 26px;
  font-weight: bold;
  text-align: center;
  padding-bottom: 38px;
}

.suggestPrintOne .mainModulePage .mainModulePageNumber span {
  color: red;
}

.suggestPrintOne .mainModulePage .mainModulePageItem {
  width: 100%;
  display: flex;
  align-items: center;
  border-top: 1px solid #b4e1ff;
  font-size: 14px;
}

.suggestPrintOne .mainModulePage .mainModulePageItem .mainModulePageLabel {
  width: 120px;
  height: 100%;
  padding: 0 2px;
  line-height: 18px;
  text-align: center;
}

.suggestPrintOne .mainModulePage .mainModulePageItem .mainModulePageValue {
  width: calc(100% - 120px);
  min-height: 40px;
  line-height: 18px;
  padding: 11px;
  border-left: 1px solid #b4e1ff;
}

.suggestPrintOne .mainModulePage .mainModulePageItem .title {
  min-height: 58px;
  display: flex;
  align-items: center;
}

.suggestPrintOne .mainModulePage .mainModulePageItem .Relevant {
  padding: 0;
}

.suggestPrintOne .mainModulePage .mainModulePageItem .Relevant .mainModulePageItem {
  border-top: 0px solid #b4e1ff;
}

.suggestPrintOne .mainModulePage .mainModulePageItem .Relevant .mainModulePageItem .mainModulePageLabel {
  width: 50%;
}

.suggestPrintOne .mainModulePage .mainModulePageItem .Relevant .mainModulePageItem .mainModulePageValue {
  width: 50%;
}

.suggestPrintOne .mainModulePage .mainModulePageItem .Relevant .mainModulePageItem+.mainModulePageItem {
  border-top: 1px solid #b4e1ff;
}

.suggestPrintOne .mainModulePage .mainModulePagecolumn {
  width: 50%;
  display: inline-block;
}

.suggestPrintOne .mainModulePage .mainModulePagecolumn+.mainModulePagecolumn {
  border-left: 1px solid #b4e1ff;
}

.suggestPrintOne .mainModulePage .mainModulePageContentLabel {
  font-size: 14px;
  line-height: 40px;
  text-align: center;
  border-top: 1px solid #b4e1ff;
  border-bottom: 1px solid #b4e1ff;
}

.suggestPrintOne .mainModulePage .pageNumber {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  text-align: right;
  font-size: 14px;
  padding-right: 32px;
  padding-bottom: 22px;
}

.suggestPrintOne .mainModulePage .mainModulePageTableName {
  font-size: 18px;
  text-align: center;
  margin-bottom: 16px;
}

.suggestPrintOne .mainModulePage .mainModulePageTable {
  width: 100%;
  table-layout: fixed;
  word-break: break-all;
  border-collapse: collapse;
  margin-bottom: 41px;
}

.suggestPrintOne .mainModulePage .TableMargin {
  margin-bottom: 0;
}

.suggestPrintOne .mainModulePage .mainModulePageTableTd {
  text-align: center;
  line-height: 24px;
  padding: 8px;
  border: 1px solid #000;
  font-family: "宋体";
  display: table-cell;
  vertical-align: middle;
}

.suggestPrintOne .mainModulePage .padding {
  padding: 20px 8px;
}

.suggestPrintOne .mainModulePage .mainModulePageContent {
  min-height: 32px;
  line-height: 32px;
  font-family: "宋体";
}

.suggestPrintOne .mainModulePage .center {
  text-align: center;
}

.suggestPrintOne .mainModulePage .right {
  text-align: right;
}

/* / .suggestPrintOne */

/*  .proposalPrintBox */
@page {
  size: auto;
  margin: 0;
  box-sizing: border-box;
}

.proposalPrintBox {
  width: 792px;
  margin: auto;
  position: fixed;
  top: -100%;
  left: -100%;
}

.proposalPrintBox .mainModulePage {
  box-shadow: 0px 3px 15px black;
  margin: 22px 0;
  padding: 88px 88px;
}

.proposalPrintBox .mainModulePageTable {
  width: 100%;
  table-layout: fixed;
  word-break: break-all;
  border-collapse: collapse;
  margin-bottom: 28px;
}

.proposalPrintBox .mainModulePageTableTd {
  text-align: center;
  line-height: 24px;
  padding: 8px;
  font-size: 21px;
  border: 1px solid #000;
  font-family: "宋体";
  display: table-cell;
  vertical-align: middle;
}

.proposalPrint {
  width: 100%;
  height: 100%;
}

.proposalPrint .mainModulePage {
  width: 100%;
  min-height: 1120px;
  max-height: 1120px;
  padding: 88px 88px;
  position: relative;
}

.proposalPrint .mainModulePage .mainModulePageType {
  font-size: 22px;
  font-weight: bold;
  padding-bottom: 68px;
}

.proposalPrint .mainModulePage .mainModulePageSlogan {
  font-size: 26px;
  font-weight: bold;
  text-align: center;
  padding-bottom: 38px;
}

.proposalPrint .mainModulePage .mainModulePageNumber {
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  padding-bottom: 88px;
}

.proposalPrint .mainModulePage .mainModulePageItem {
  width: 100%;
  display: flex;
  font-size: 22px;
  line-height: 26px;
  padding-bottom: 22px;
}

.proposalPrint .mainModulePage .mainModulePageItem span {
  display: inline-block;
}

.proposalPrint .mainModulePage .mainModulePageItem .mainModulePageItemTitle {
  width: 92px;
  font-weight: bold;
  text-align: justify;
  text-align-last: justify;
}

.proposalPrint .mainModulePage .mainModulePageItem .mainModulePageItemColon {
  width: 16px;
}

.proposalPrint .mainModulePage .mainModulePageItem .mainModulePageItemContent {
  width: calc(100% - 108px);
  border-bottom: 1px solid #262626;
}

.proposalPrint .mainModulePage .mainModulePageItemB {
  padding-bottom: 66px;
}

.proposalPrint .mainModulePage .mainModulePageItemTwo {
  width: 100%;
  height: 27px;
  font-size: 22px;
  min-height: 27px;
  line-height: 26px;
  border-bottom: 1px solid #262626;
}

.proposalPrint .mainModulePage .mainModulePageItemFlex {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.proposalPrint .mainModulePage .mainModulePageItemFlex .mainModulePageItem {
  width: 46%;
}

.proposalPrint .mainModulePage .mainModulePageItemTime {
  text-align: center;
  font-size: 23px;
  padding-top: 120px;
  padding-bottom: 22px;
  font-family: "Times New Roman";
}

.proposalPrint .mainModulePage .pageNumber {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  text-align: right;
  font-size: 14px;
  padding-right: 32px;
  padding-bottom: 22px;
}

.proposalPrint .mainModulePage .mainModulePageTableName {
  font-size: 21px;
  text-align: center;
  margin-bottom: 16px;
}

.proposalPrint .mainModulePage .mainModulePageTable {
  width: 100%;
  table-layout: fixed;
  word-break: break-all;
  border-collapse: collapse;
  margin-bottom: 41px;
}

.proposalPrint .mainModulePage .TableMargin {
  margin-bottom: 0;
}

.proposalPrint .mainModulePage .mainModulePageTableTd {
  text-align: center;
  line-height: 24px;
  padding: 8px;
  font-size: 21px;
  border: 1px solid #000;
  font-family: "宋体";
  display: table-cell;
  vertical-align: middle;
}

.proposalPrint .mainModulePage .padding {
  padding: 20px 8px;
}

.proposalPrint .mainModulePage .mainModulePageContent {
  font-size: 21px;
  min-height: 36px;
  line-height: 36px;
  font-family: "宋体";
}

.proposalPrint .mainModulePage .center {
  text-align: center;
}

.proposalPrint .mainModulePage .right {
  text-align: right;
}

/*  / .proposalPrintBox */

/* 机关办公 */

.table-approval {
  width: 100%;
  height: 100%;
  padding: 0 25px;
  box-sizing: border-box;
}

.table-approval .table-box {
  position: relative;
}

.table-approval .table-box .Printing {
  position: absolute;
  right: 45px;
  top: 20px;
}

.table-approval .table-box .table-title {
  line-height: 100px;
  height: 100px;
  font-size: 32px;
  color: #ec0e22;
  text-align: center;
  font-family: 'SimSun';
  font-weight: 700;
}

.table-approval .table-box .table-column {
  border: 2px solid #ec0e22;
}

.table-approval .table-box .table-column .table-row {
  display: flex;
  position: relative;
}

.table-approval .table-box .table-column .table-row .table-row-item {
  display: flex;
  position: relative;
  flex: auto;
}

.table-approval .table-box .table-column .table-row .table-row-item .table-row-item-lable {
  width: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  min-height: 60px;
  font-size: 18px;
  color: red;
  padding: 10px;
  flex-shrink: 0;
  font-family: 'SimHei';
  font-weight: 600;
}

.table-approval .table-box .table-column .table-row .table-row-item .table-row-item-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  font-size: 14px;
  min-height: 60px;
  padding: 0 8px;
  font-family: 'FangSong';
}

.table-approval .table-box .table-column .table-row .table-row-item .table-row-item-content i {
  font-style: normal;
}

.table-approval .table-box .table-column .table-row .table-row-item .table-file {
  justify-content: flex-start !important;
}

.table-approval .table-box .table-column .table-row .table-row-item .table-file>i {
  margin: 0 8px;
  font-size: 24px;
  color: #ec0e22;
}

.table-approval .table-box .table-column .table-row .table-row-item .table-file .file-list>p>i {
  display: none;
  cursor: pointer;
}

.table-approval .table-box .table-column .table-row .table-row-item .table-file .file-list>p:hover>i {
  display: inline-block;
}

.table-approval .table-box .table-column .table-row .table-row-item .node-check-user {
  justify-content: flex-start;
}

.table-approval .table-box .table-column .table-row .table-row-item .text-left {
  justify-content: flex-start;
}

.table-approval .table-box .table-column .table-row .table-row-item .text-left>span {
  margin-right: 15px;
}

.table-approval .table-box .table-column .table-row .table-row-item .red-head {
  color: #ec0e22;
  font-size: 18px;
  font-family: 'SimHei';
  font-weight: 600;
}

.table-approval .table-box .table-column .table-row .table-row-item .red-head>span {
  color: #444;
  font-family: 'FangSong';
  font-size: 14px;
}

.table-approval .table-box .table-column .table-row-column {
  min-height: 100%;
  flex: auto;
}

.table-approval .table-box .table-column .border-width1-top {
  border-top: 1px solid #ec0e22;
}

.table-approval .table-box .table-column .border-width1-left {
  border-left: 1px solid #ec0e22;
}

.table-approval .table-box .table-column .border-width1-right {
  border-right: 1px solid #ec0e22;
}

.table-approval .table-box .table-column .border-width1-bottom {
  border-bottom: 1px solid #ec0e22;
}

.table-approval .table-box .table-column .border-width2-top {
  border-top: 2px solid #ec0e22;
}

.table-approval .table-box .table-column .border-width2-right {
  border-right: 2px solid #ec0e22;
}

.table-approval .table-box .table-column .border-width2-left {
  border-left: 2px solid #ec0e22;
}

.table-approval .table-box .table-column .border-width2-bottom {
  border-bottom: 2px solid #ec0e22;
}

.table-approval .table-box .table-select .el-input--suffix .el-input__inner {
  border: none;
}

.table-approval .table-box .table-input {
  align-self: center;
}

.table-approval .table-box .table-input>.el-input__inner {
  border: none !important;
}

.table-approval .table-box .table-date {
  border: none !important;
}

.table-approval .table-box .table-date-time>.el-input__inner {
  border: none;
}

.table-approval .table-box .bankInfo p {
  line-height: 28px;
  font-family: 'FangSong';
  font-size: 14px;
  padding-left: 12px;
}

.table-approval .table-box .table-disabled {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #595959;
  opacity: 0.10;
  z-index: 1000;
}

.table-approval .table-box .table-icon-btn {
  background-color: #d6d6d6;
  border-color: #eee;
  color: #ec0e22;
  align-self: center;
  margin-left: 4px;
  padding: 4px;
  font-size: 12px;
}

.table-approval .table-box .table-icon-btn:hover {
  background-color: #EfEfEf;
  border-color: #eee;
}

.table-approval .table-box .table-icon-btn:focus {
  background-color: #EfEfEf;
  border-color: #eee;
}

.table-approval .table-box .table-icon-btn:active {
  background-color: #EfEfEf;
  border-color: #eee;
}

.table-approval .table-box .table-span {
  align-self: center;
  margin: 0 8px;
}

.table-approval .table-box .table-tips {
  font-size: 12pt;
  color: #ec0e22;
}

.table-approval .table-box .costList {
  width: 100%;
}

.table-approval .table-box .costList .costList-item {
  display: flex;
  font-family: "FangSong";
  height: 36px;
  border-bottom: 1px solid #ec0e22;
  box-sizing: border-box;
  line-height: 36px;
}

.table-approval .table-box .costList .costList-item .name {
  width: 120px;
  text-align: center;
}

.table-approval .table-box .costList .costList-item .value {
  flex: auto;
  padding-left: 20px;
}

.table-approval .table-box .costList .costList-item:last-child {
  border-bottom: none;
}

.table-approval .table-box .verify {
  flex-direction: column;
  padding: 10px;
  width: 100%;
}

.table-approval .table-box .verify .table-verify-select {
  width: 100%;
}

.table-approval .table-box .verify .table-verify-content {
  padding: 10px 0;
  width: 100%;
}

.table-approval .table-box .verify .table-verify-btn {
  width: 100%;
  padding-right: 30px;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-end;
}

.table-approval .table-box .verify .table-verify-btn .signature-img {
  height: 36px;
  margin-left: 24px;
}

.table-approval .table-box .signature {
  display: flex;
  align-items: center;
  font-size: 14px;
  position: relative;
}

.table-approval .table-box .signature .sign {
  width: 169px;
  height: 100%;
  min-height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  font-size: 14px;
  font-family: 'FangSong';
}

.table-approval .table-box .signature .sign>img {
  max-width: 100%;
  max-height: 48px;
}

.table-approval .table-box .signature .opition {
  flex: auto;
  padding: 8px 10px;
}

.table-approval .table-box .signature .opition>p {
  font-family: 'FangSong';
}

.table-approval .table-box .signature-record {
  height: 60px;
}

.table-approval .table-th-lable {
  color: red;
  font-family: 'SimHei';
  font-weight: 600;
}

.table-approval .btn-box {
  height: 54px;
  line-height: 54px;
  text-align: center;
  width: 1000px;
}

.table-approval .flex1 .table-row-item {
  flex: 1 !important;
}

.table-approval .files {
  flex-direction: column;
  align-items: flex-start !important;
}

.table-approval .node-label {
  padding: 0 15px !important;
}

.table-approval .downloadfile {
  cursor: pointer;
  font-family: 'FangSong';
}

.table-approval .downloadfile:hover {
  color: #199bc5;
}

.table-approval .max-ht {
  height: 100% !important;
}

@media print {
  .table-approval .table-row-item .text-left>span {
    margin-right: 5px !important;
  }

  .table-approval .signature .signature-record .max-ht {
    height: 100% !important;
  }

  .table-approval .table-box .signature .sign {
    height: 100% !important;
  }

  .table-approval .table-box .signature .sign>img {
    min-height: 60px;
    margin: 5px 0;
    width: calc(100% - 10px);
  }
}

.apply-car-table .passenger {
  display: flex;
}

.apply-car-table .passenger .passenger-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 45px;
  line-height: 45px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.apply-car-table .deploy-car {
  display: flex;
}

.apply-car-table .deploy-car .deploy-car-item {
  display: flex;
  font-size: 10px;
  justify-content: center;
  align-items: center;
  height: 45px;
  line-height: 45px;
  flex-shrink: 0;
}

.apply-car-table .deploy-car-th .deploy-car-item {
  color: red;
}

.apply-car-table .get-out {
  display: flex;
}

.apply-car-table .get-out .get-out-item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 45px;
  padding: 0 4px;
  line-height: 45px;
  flex-shrink: 0;
}

.apply-car-table .get-out .get-out-item>p {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.apply-car-table .car-info {
  width: 100px;
}

.apply-car-table .car-num {
  width: 100px;
}

.apply-car-table .car-time {
  width: 140px;
}

.apply-car-table .car-mile {
  width: 125px;
}

.apply-car-table .car-opreate {
  width: 108px;
}

.apply-car-table .get-out-th>.get-out-item {
  font-family: 'SimHei';
  color: red;
}

.apply-car-table .get-out-txt>.get-out-item {
  font-family: 'FangSong';
  font-size: 14px;
}

.apply-car-table .fs {
  font-family: 'FangSong';
  font-size: 14px;
}

.apply-car-table .car-img-box {
  width: 400px;
  padding: 15px;
  box-sizing: border-box;
}

.apply-car-table .car-img-box .car-btn-box {
  display: flex;
  height: 54px;
  align-items: center;
}

.reception-table .letter-left {
  width: calc(100%/3);
}

.reception-table .letter-right {
  width: calc(100%/3*2);
}

.reception-table .guest {
  display: flex;
}

.reception-table .guest .guest-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 45px;
  line-height: 45px;
  font-family: 'FangSong';
}

.reception-table .guest-remark {
  height: 45px;
  line-height: 45px;
  padding: 0 10px;
  font-family: 'FangSong';
}

.reception-table .reception-info-item {
  display: flex;
  height: 45px;
  line-height: 45px;
}

.reception-table .reception-info-item .reception-info-item-label {
  width: 169px;
  display: flex;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  font-family: 'FangSong';
}

.reception-table .reception-info-item .reception-info-item-content {
  flex: auto;
  align-items: center;
  font-family: 'FangSong';
  padding: 0 10px;
}

.apply-goods-table .goods-item {
  font-size: 14px;
  padding: 0 8px;
  font-family: 'FangSong';
  line-height: 30px;
}

.apply-goods-table .Purchase-list {
  display: flex;
}

.apply-goods-table .Purchase-list .Purchase-list-item {
  flex: 1;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-family: 'FangSong';
}

.sp-bottom:last-child {
  border-bottom: none !important;
}