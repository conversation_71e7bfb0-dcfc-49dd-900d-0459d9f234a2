// 导入封装的方法
import {
  post,
  get,
  fileRequest,
  postform,
  exportFile
} from '../http'
const appManagement = {
  changeAuditIsTop (params) {
    // 企业联谊是否置顶
    return post('/friendshipassociation/updateTop', params)
  },
  changeAuditStatus (params) {
    // 企业联谊审核
    return post('/friendshipassociation/updateState', params)
  },
  corporateNewsList (params) {
    // 所有联谊列表
    return post('/friendshipassociation/list?', params)
  },
  getFellowshipDetail (id) {
    // 获取企业联谊详情
    return post(`/friendshipassociation/info/${id}`)
  },
  corporateNewsListDel (params) {
    // 企业联谊列表删除
    return post('/friendshipassociation/dels', params)
  },
  getFellowshipColumnDetail (id) {
    // 获取联谊栏目详情-栏目
    return post(`/friendshipassociationcolumn/info/${id}`)
  },
  getFellowshipColumn (params) {
    // 获取联谊栏目列表-栏目
    return post('/friendshipassociationcolumn/list?pageNo=1&pageSize=500')
  },
  getFellowshipColumnTwo (params) {
    // 获取联谊栏目列表-栏目
    return post('/friendshipassociationcolumn/list', params)
  },
  delFellowshipColumn (params) {
    // 删除联谊栏目列表-栏目
    return post('/friendshipassociationcolumn/dels', params)
  },

  informationList (params) {
    return post('/zyinfodetail/list?', params)
  },
  informationListInfo (params) {
    return post(`/zyinfodetail/partyBranch/info/${params}`)
  },
  getstructure (params) {
    return post('/valley/getstructure', params)
  },
  pushInfo (params) {
    return post('/valley/pushInfo', params)
  },
  officeList (params) {
    return post('/infostructurerelation/list?', params)
  },
  infostructurerelation (params) {
    return post('/infostructurerelation/dels', params)
  },
  informationListDel (params) {
    return post('/zyinfodetail/dels', params)
  },
  editInfo (params) {
    return post('/valley/editInfo', params)
  },
  informationBatchUpdate (params) {
    return post('/zyinfodetail/batchUpdate', params)
  },
  picList (params) {
    return post('/zyinforeportpic/pic/list?', params)
  },
  picInfo (params) {
    return post(`/zyinforeportpic/pic/info/${params}`)
  },
  picDel (params) {
    return post('/zyinforeportpic/dels', params)
  },
  editIsCheck (params) {
    return post('/zyinforeportpic/scrolling/report/editIsCheck', params)
  },
  reportList (params) {
    return post('/zyinforeportpic/scrolling/report/list?', params)
  },
  reportInfo (params) {
    return post(`/zyinforeportpic/scrolling/report/${params}`)
  },
  pictureList (params) {
    return post('/zyinforeportpic/scrolling/pic/list?', params)
  },
  pictureInfo (params) {
    return post(`/zyinforeportpic/scrolling/pic/${params}`)
  },
  associatedList (params) {
    return post('/inforelation/list?', params)
  },
  associatedAddList (params) {
    return post('/inforelation/addList?', params)
  },
  associatedAdd (params) {
    return post('/inforelation/add', params)
  },
  associatedDel (params) {
    return post('/inforelation/dels', params)
  },
  informationColumn (params) {
    return post('/zyinfostructure/list?', params)
  },
  informationColumnTree (params) {
    return post('/zyinfostructure/tree', params)
  },
  informationColumnInfo (params) {
    return post(`/zyinfostructure/info/${params}`)
  },
  informationColumnDel (params) {
    return post('/zyinfostructure/dels', params)
  },
  customTopicList (params) {
    return post('/specialsubjectinfo/list?', params)
  },
  customTopicListInfo (params) {
    return post(`/specialsubjectinfo/info/${params}`)
  },
  customTopicListDel (params) {
    return post('/specialsubjectinfo/dels', params)
  },
  customTopicColumnList (params) {
    return post('/zySpecialsubjectColumn/list', params)
  },
  customTopicColumnTree (params) {
    return post('/zySpecialsubjectColumn/treeDataBySubject', params)
  },
  customTopicColumnInfo (params) {
    return post('/zySpecialsubjectColumn/form', params)
  },
  customTopicColumnDel (params) {
    return post('/zySpecialsubjectColumn/delete', params)
  },
  customTopicinformation (params) {
    return post('/specialsubjectnews/list?', params)
  },
  customTopicinformationInfo (params) {
    return post(`/specialsubjectnews/info/${params}`)
  },
  customTopicinformationDel (params) {
    return post('/specialsubjectnews/dels', params)
  },
  projectAssociatedList (params) {
    return post('/zySpecialsubjectRelateinfo/list', params)
  },
  projectAssociatedrecord (params) {
    return post('/zySpecialsubjectRelateinfo/subjectNews', params)
  },
  projectAssociatednew (params) {
    return post('/zySpecialsubjectRelateinfo/add', params)
  },
  projectAssociatedDel (params) {
    return post('/zySpecialsubjectRelateinfo/delete', params)
  },
  moduleList (params) {
    return post('/module/list?', params)
  },
  moduleInfo (params) {
    return post(`/module/info/${params}`)
  },
  moduleDel (params) {
    return post('/module/dels', params)
  },
  areaTree (params) {
    return post('/area/tree?', params)
  },
  moduleTree (params) {
    return post('/module/tree', params)
  },
  fileinfoList (params) {
    return post('/fileinfo/list?', params)
  },
  downloadFile (params, text) {
    fileRequest(`/fileinfo/download/${params}`, {}, text)
  },
  fileinfoAdd (params) {
    return postform('/fileinfo/add?', params)
  },
  fileinfoInfo (params) {
    return postform(`/fileinfo/info/${params}`)
  },
  fileinfoDel (params) {
    return postform(`/fileinfo/del/${params}`)
  },
  // postname (params) {
  //   return post('http://192.168.1.109:80/fillgeneral/saveFillGeneral', params)
  // },
  /**
   * 获取代表说列表
   */
  committeesayList (params) {
    return post('/committeesay/list', params)
  },
  /**
   * 修改代表说
   */
  committeesayEdit (params) {
    return post('/committeesay/edit', params)
  },
  /**
   * 添加代表说
   */
  committeesayAdd (params) {
    return post('/committeesay/add', params)
  },
  /**
   * 代表说详情
   */
  committeesayInfo (params) {
    return post('/committeesay/info/' + params)
  },
  /**
   * 批量删除代表说
   */
  committeesayDels (params) {
    return post('/committeesay/dels', params)
  },
  /**
   * 批量审核代表说
   */
  committeesayBatchUpdate (params) {
    return post('/committeesay/batchUpdate', params)
  },
  /**
   * 获取晒一晒列表
   */
  showyourselfList (params) {
    return post('/showyourself/list', params)
  },
  /**
   * 修改晒一晒
   */
  showyourselfEdit (params) {
    return post('/showyourself/edit', params)
  },
  /**
   * 添加晒一晒
   */
  showyourselfAdd (params) {
    return post('/showyourself/add', params)
  },
  /**
   * 晒一晒详情
   */
  showyourselfInfo (params) {
    return post('/showyourself/info/' + params)
  },
  /**
   * 批量删除晒一晒
   */
  showyourselfDels (params) {
    return post('/showyourself/dels', params)
  },
  /**
   * 批量审核晒一晒
   */
  showyourselfBatchUpdate (params) {
    return post('/showyourself/batchUpdate', params)
  },
  /**
   * 通讯录树
   */
  treeTist (params) {
    return post('/tree/list?', params)
  },
  /**
   * 通讯录用户
   */
  treeTistUser (params) {
    return post('/wholeuser/chaters?', params)
  },
  /**
   * 通讯录新增用户
   */
  userelationNew (params) {
    return post('/userelation/save?', params)
  },
  /**
   * 通讯录删除用户
   */
  userelationDel (params) {
    return post('/wholeuser/removechater?', params)
  },
  /**
   * 通讯录分组详情
   */
  treeTistInfo (params) {
    return post(`/tree/info/${params}`)
  },
  /**
   * 通讯录分组删除
   */
  treeTistDel (params) {
    return post(`/tree/del/${params}`)
  },
  /**
   * 用户群组列表
   */
  groupList (params) {
    return post('/talkroup/list', params)
  },
  /**
   * 分配群用户
   */
  groupUser (params) {
    return post('/talkroup/join?', params)
  },
  /**
   * 解散群
   */
  disband (params) {
    return post(`/talkroup/disband/${params}`)
  },
  /**
   * 群成员列表
   */
  groupUserList (params) {
    return post('/talkroup/member?', params)
  },
  // 新增开头语
  openingWordSave (data) {
    return post('/rd/assistant/welcome/add', data)
  },
  // 开头语列表
  openingWordList (data) {
    return get('/rd/assistant/welcome/list', data)
  },
  // 编辑开头语
  openingWordEdit (data) {
    return post('/rd/assistant/welcome/edit', data)
  },
  // 关键词新增
  specialKeywordSave (data) {
    return post('/rd/assistant/question/add', data)
  },
  // 特殊关键词列表
  specialKeywordList (data) {
    return get('/rd/assistant/question/list', data)
  },
  // 关键词编辑
  specialKeywordEdit (data) {
    return post('/rd/assistant/question/edit', data)
  },
  specialKeywordDels (data) {
    return post('/rd/assistant/question/dels', data)
  },
  // 获取和app扫码配置值
  getAppconfigCode () {
    return get('/readonfig?codes=rongCloudIdPrefix')
  },
  joinappusertype (data) {
    return post('/wholeuser/joinapp/usertype', data)
  },
  joinapp (data) {
    return post('/wholeuser/joinapp?', data)
  },
  yearsummarygrouplist (data) { // 分组列表
    return post('/yearsummarygroup/list?', data)
  },
  yearsummarygroup (url, data) { // 添加 修改
    return post(url, data)
  },
  yearsummarygroupdels (data) { // 删除分组
    return post('/yearsummarygroup/dels', data)
  },
  yearsummarygroupinfo (data) { // 详情
    return post(`yearsummarygroup/info/${data}`)
  },
  yearsummarylist (data) { // 模块列表
    return post('/yearsummary/list?', data)
  },
  yearsummary (url, data) { // 添加 修改
    return post(url, data)
  },
  yearsummarydels (data) { // 删除模块
    return post('/yearsummary/dels', data)
  },
  yearsummaryinfo (data) { // 详情
    return post(`/yearsummary/info/${data}`)
  },
  yearsummaryselects (data) { // 详情
    return post('/yearsummary/selects')
  },
  yearsummarygenerate (data) { // 生成年度报告
    return post('/yearsummary/generate', data)
  },
  // 上传附件
  uploadFile (data) {
    return postform('/attachment/uploadFile', data, {
      timeout: 80000
    })
  },
  // 启动图片管理
  splashImgManage: {
    list (params) {
      return post('/themestartup/list', params)
    },
    add (params) {
      return post('/themestartup/add', params)
    },
    edit (params) {
      return post('/themestartup/edit', params)
    },
    info (id) {
      return get(`/themestartup/info/${id}`)
    },
    del (id) {
      return get(`/themestartup/del/${id}`)
    },
    dels (ids) {
      return post('/themestartup/dels', {
        ids: ids
      })
    },
    verify (params) {
      return post('/themestartup/editIsUse', params)
    }
  },
  // 背景图管理
  backgroundImgManage: {
    list (params) {
      return post('/themebackground/list', params)
    },
    add (params) {
      return post('/themebackground/add', params)
    },
    edit (params) {
      return post('/themebackground/edit', params)
    },
    info (id) {
      return get(`/themebackground/info/${id}`)
    },
    del (id) {
      return get(`/themebackground/del/${id}`)
    },
    dels (ids) {
      return post('/themebackground/dels', {
        ids: ids
      })
    },
    verify (params) {
      return post('/themebackground/editIsUse', params)
    }
  },
  themeContent (params) {
    return post('/specialsubjectinfo/details', params)
  },
  treeDataBySubjectDetails (params) {
    return post('/zySpecialsubjectColumn/treeDataBySubjectDetails', params)
  },
  // 投票
  vote: {
    list (params) {
      return post('/qdvote/list', params)
    },
    count (params) {
      return post('/qdvote/count', params)
    },
    countselected (params) {
      return post('/qdvote/countselected', params)
    },
    add (params) {
      return post('/qdvote/add', params, {
        'Content-Type': 'application/json;charset=UTF-8'
      })
    },
    edit (params) {
      return post('/qdvote/edit', params, {
        'Content-Type': 'application/json;charset=UTF-8'
      })
    },
    info (id) {
      return get(`/qdvote/info/${id}`)
    },
    dels (ids) {
      return post('/qdvote/dels', {
        ids
      })
    },
    publish (params) {
      return post('/qdvote/updIsPublish', params)
    },
    previewCode (id) {
      return post('/qdvote/getQrVote', {
        id
      })
    }
  },
  // 投票选项
  voteQuestion: {
    list (params) {
      return post('/qdvoteoptions/list', params)
    },
    add (params) {
      return post('/qdvoteoptions/add', params)
    },
    edit (params) {
      return post('/qdvoteoptions/edit', params)
    },
    info (id) {
      return post(`/qdvoteoptions/info/${id}`)
    },
    dels (ids) {
      return post('/qdvoteoptions/dels', {
        ids
      })
    },
    template () {
      return exportFile('/qdvoteoptions/importemplate')
    },
    import (params) {
      return postform('/qdvoteoptions/import', params)
    }
  },
  getIsTransactionOrg () {
    return post('/oversee/getIsTransactionOrg')
  },
  questionnaire: {
    list (params) {
      return post('/questionnaire/list', params)
    },
    info (id) {
      return post(`/questionnaire/info/${id}`)
    },
    add (params) {
      return post('/questionnaire/add', params, {
        'Content-Type': 'application/json;charset=UTF-8'
      })
    },
    edit (params) {
      return post('/questionnaire/edit', params, {
        'Content-Type': 'application/json;charset=UTF-8'
      })
    },
    del (id) {
      return post(`questionnaire/del/${id}`)
    },
    dels (params) {
      return post('questionnaire/dels/', params)
    },
    statisticsData (params) {
      return post('/questionnaire/statisticsData', params)
    }
  },
  questionnairequestionbank: {
    list (params) {
      return post('/questionnairequestionbank/list', params)
    },
    info (id) {
      return post(`/questionnairequestionbank/info/${id}`)
    },
    add (params) {
      return post('/questionnairequestionbank/add', params)
    },
    edit (params) {
      return post('/questionnairequestionbank/edit', params)
    },
    del (id) {
      return post(`questionnairequestionbank/del/${id}`)
    },
    dels (params) {
      return post('questionnairequestionbank/dels/', params)
    }
  }
}
export default appManagement
