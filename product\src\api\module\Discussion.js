import { post } from '../http'

const Discussion = {

  // 协商在线管理列表接口
  surveylist (params) {
    return post('/survey/list', params)
  },
  // 协商在线管理新增接口
  surveyadd (url, params) {
    return post(url, params)
  },
  // 协商在线管理修改接口
  surveyedit (url, params) {
    return post(url, params)
  },

  // 协商背景列表接口
  backgroundList (params) {
    return post('/surveybackground/list', params)
  },
  // 协商背景新增接口
  backgroundAdd (url, params) {
    return post(url, params)
  },
  // 协商背景修改接口
  backgroundEdit (url, params) {
    return post(url, params)
  },
  // 协商背景详情接口
  backgroundInfo (id) {
    return post(`/surveybackground/info/${id}`)
  },
  // 协商背景删除接口
  backgroundDel (id) {
    return post(`/surveybackground/del/${id}`)
  },
  // 协商背景批量删除接口
  backgroundDels (params) {
    return post('/surveybackground/dels', params)
  },
  // 协商背景审核通过接口
  backgroundCheck (params) {
    return post('/surveybackground/check', params)
  },
  // 协商背景审核不通过接口
  backgroundCheckBack (params) {
    return post('/surveybackground/checkBack', params)
  },

  // 协商成果列表接口
  NegotiationList (params) {
    return post('/surveyresult/list', params)
  },
  // 协商成果新增接口
  NegotiationAdd (url, params) {
    return post(url, params)
  },
  // 协商成果详情接口
  NegotiationInfo (id) {
    return post(`/surveyresult/info/${id}`)
  },
  // 协商成果删除接口
  NegotiationDel (id) {
    return post(`/surveyresult/del/${id}`)
  },
  // 协商成果批量删除接口
  NegotiationDels (params) {
    return post('/surveyresult/dels', params)
  },
  // 协商成果审核通过接口
  NegotiationCheck (params) {
    return post('/surveyresult/check', params)
  },
  // 协商成果审核不通过接口
  NegotiationCheckBack (params) {
    return post('/surveyresult/checkBack', params)
  },

  // 留言列表接口
  messageList (params) {
    return post('/surveymessage/list', params)
  },
  // 留言新增接口
  messageAdd (url, params) {
    return post(url, params)
  },
  // 留言编辑接口
  messageEdit (url, params) {
    return post(url, params)
  },
  // 留言详情接口
  messageInfo (id) {
    return post(`/surveymessage/info/${id}`)
  },
  // 留言删除接口
  messageDel (id) {
    return post(`/surveymessage/del/${id}`)
  },
  // 留言批量删除接口
  messageDels (params) {
    return post('/surveymessage/dels', params)
  },
  // 留言审核通过接口
  messageCheck (params) {
    return post('/surveymessage/check', params)
  },
  // 留言审核不通过接口
  messageCheckBack (params) {
    return post('/surveymessage/checkBack', params)
  },
  // 公众留言列表接口
  publicmessageList (params) {
    return post('/surveypublicmessage/list', params)
  },
  // 公众留言详情接口
  publicmessageInfo (id) {
    return post(`/surveypublicmessage/info/${id}`)
  },
  // 公众留言审核通过接口
  publicmessagecheck (params) {
    return post('/surveypublicmessage/check', params)
  },
  // 公众留言删除接口
  publicmessageDels (params) {
    return post('/surveypublicmessage/dels', params)
  },
  // 公众留言审核不通过接口
  publicmessagecheckBack (params) {
    return post('/surveypublicmessage/checkBack', params)
  },
  // 回复页面列表的接口
  replyList (params) {
    return post('/surveypublicreply/list', params)
  },
  // 回复页面新增的接口
  replyAdd (url, params) {
    return post(url, params)
  },
  // 回复页面修改的接口
  replyEdit (url, params) {
    return post(url, params)
  },
  // 回复页面批量删除的接口
  replyDels (params) {
    return post('/surveypublicreply/dels', params)
  },
  // 回复页面删除的接口
  replyDel (id) {
    return post(`/surveypublicreply/del/${id}`)
  },
  // 回复页面详情的接口
  replyInfo (id) {
    return post(`/surveypublicreply/info/${id}`)
  },
  // 协商部门管理列表
  surveydepartmentList (params) {
    return post('/surveydepartment/list', params)
  },
  // 协商部门管理新增
  surveydepartmentAdd (url, params) {
    return post(url, params)
  },
  // 协商部门管理编辑
  surveydepartmentEdit (url, params) {
    return post(url, params)
  },
  // 协商部门管理详情
  surveydepartmentInfo (id) {
    return post(`/surveydepartment/info/${id}`)
  },
  // 协商部门管理详情
  surveydepartmentDels (params) {
    return post('/surveydepartment/dels', params)
  },
  // 协商部门管理启用和禁用
  surveydepartmentState (params) {
    return post('/surveydepartment/batchUpdateState', params)
  },
  // 协商在线管理栏目管理列表
  surveycolumnList (params) {
    return post('/surveycolumn/list', params)
  },
  // 侧边栏目数据
  surveycolumnListleft (params) {
    return post('/surveycolumn/treeDataBySurvey', params)
  },
  // 协商在线管理栏目管理新增
  surveycolumnAdd (url, params) {
    return post(url, params)
  },
  // 协商在线管理栏目管理编辑
  surveycolumnEdit (url, params) {
    return post(url, params)
  },
  // 协商在线管理栏目管理详情
  surveycolumnInfo (id) {
    return post(`/surveycolumn/info/${id}`)
  },
  // 协商在线管理栏目管理删除
  surveycolumnDels (params) {
    return post('/surveycolumn/dels', params)
  },
  // 协商在线管理专题列表
  surveyspecialList (params) {
    return post('/surveyspecial/list', params)
  },
  // 协商在线管理专题详情
  surveyspecialInfo (id) {
    return post(`/surveyspecial/info/${id}`)
  },
  // 协商在线管理专题新增
  surveyspecialAdd (params) {
    return post('/surveyspecial/add', params)
  },
  // 协商在线管理专题编辑
  surveyspecialEdit (params) {
    return post('/surveyspecial/edit', params)
  },
  // 协商在线管理专题删除
  surveyspecialDels (params) {
    return post('/surveyspecial/dels', params)
  },
  // 协商工作计划列表
  surveyworkplanList (params) {
    return post('/surveyworkplan/list', params)
  },
  // 协商工作计划新增
  surveyworkplanAdd (url, params) {
    return post(url, params)
  },
  // 协商工作计划编辑
  surveyworkplanEdit (url, params) {
    return post(url, params)
  },
  // 协商工作计划详情
  surveyworkplanInfo (id) {
    return post(`/surveyworkplan/info/${id}`)
  },
  // 协商工作计划删除
  surveyworkplanDels (params) {
    return post('/surveyworkplan/dels', params)
  },
  // 专题内容左侧导航
  surveyworkplanleft (params) {
    return post('/surveyworkplan/dels', params)
  },

  // 协商计划封面图列表
  surveyconfigList (params) {
    return post('/surveyconfig/list', params)
  },
  // 协商计划封面图新增编辑
  surveyconfigAddEdit (url, params) {
    return post(url, params)
  },
  // 协商计划封面图新增详情
  surveyconfigInfo (id) {
    return post(`/surveyconfig/info/${id}`)
  },
  surveyconfigDels (params) {
    return post('/surveyconfig/dels', params)
  }
}

export default Discussion
