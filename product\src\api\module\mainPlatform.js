import { post, postform } from '../http'

const mainPlatform = {
  joinapp (params) {
    return post('/wholeuser/joinapp?', params)
  },
  pcAllLogUsers (params) {
    return post('/pcAllLogUsers', params)
  },
  appallogusers (params) {
    return post('/appallogusers?', params)
  },
  // committeesayAppList (params) {
  //   return post('/committeesay/listForShow?', params)
  // },
  committeesayAppList (params) {
    return post('/commentCircle/listTreePc', params)
  },
  committeesayAppDel (params) {
    return post(`/commentCircle/del/${params}`)
  },
  commentDel (params) {
    return post(`/commentInfo/del/${params}`)
  },
  fabulousSave (params) {
    return post('/fabulous/save', params)
  },
  fabulousDel (params) {
    return post('/fabulous/del', params)
  },
  // 通讯录分组列表
  chatterGroupList (params) {
    return post('/chatter/group/list?', params)
  },
  // 通讯录分组详情
  chatterGroupInfo (params) {
    return post(`/chatter/group/info/${params}`)
  },
  // 通讯录分组删除
  chatterGroupDel (params) {
    return post(`/chatter/group/del/${params}`)
  },
  // 通讯录共享不共享
  chatterGroupShare (params) {
    return post('/chatter/group/share?', params)
  },
  chatterSelfList (params) {
    return post('/chatter/selfList?', params)
  },
  chatterSave (params) {
    return post('/chatter/save?', params)
  },
  chatterRemove (params) {
    return post('/chatter/remove?', params)
  },
  chatterShare (params) {
    return post('/chatter/share?', params)
  },
  existUser (params) {
    return post('/chatter/existUser?', params)
  },
  provinceCount (params) {
    return post('/chatter/provinceCount?', params)
  },
  provinceArea (params) {
    return post('/chatter/provinceArea?', params)
  },
  provinceList (params) {
    return post('/chatter/provinceList?', params)
  },
  noticereturnAdd (params) {
    return post('/noticereturn/add', params)
  },
  myHeadimg (params) {
    return postform('/file/myHeadimg', params)
  },
  groupAppList (params) {
    return postform('/chatter/group/appList', params)
  }
}
export default mainPlatform
