import {
  post
} from '../http'
const meetSpeech = {
  getSpeechNoticeMap (params) {
    return post('/speechnotice/getSpeechNoticeMap', params)
  },
  conferencespeech: {
    list (params) {
      return post('/conferencespeech/list', params)
    },
    info (params) {
      return post(`/conferencespeech/info/${params}`)
    },
    add (params) {
      return post('/conferencespeech/add', params)
    },
    edit (params) {
      return post('/conferencespeech/edit', params)
    },
    dels (params) {
      return post('/conferencespeech/dels', params)
    }
  },
  adoptsConferenceSpeech (params) {
    return post('/conferencespeech/adoptsConferenceSpeech', params)
  },
  selectedConferenceSpeech (params) {
    return post('/conferencespeech/selectedConferenceSpeech', params)
  },
  sendConferenceSpeechSms (params) {
    return post('/conferencespeech/sendConferenceSpeechSms', params)
  },
  findMyConferenceSpeechs (params) {
    return post('/conferencespeech/findMyConferenceSpeechs', params)
  },
  findConferenceSpeechDrafts (params) {
    return post('/conferencespeech/findConferenceSpeechDrafts', params)
  },
  findConferenceSpeechDraftsDels (params) {
    return post('/conferencespeech/dels', params)
  },
  findConferenceSpeechToAdopts (params) {
    return post('/conferencespeech/findConferenceSpeechToAdopts', params)
  },
  findConferenceSpeechCeSelecteds (params) {
    return post('/conferencespeech/findConferenceSpeechCeSelecteds', params)
  },
  referenceRecord (params) {
    return post('/conferencespeech/referenceRecord', params)
  },
  updateIsPublic (params) {
    return post('/conferencespeech/updateIsPublic', params)
  },
  speechnotice: {
    list (params) {
      return post('/speechnotice/list', params)
    },
    info (params) {
      return post(`/speechnotice/info/${params}`)
    },
    add (params) {
      return post('/speechnotice/add', params)
    },
    edit (params) {
      return post('/speechnotice/edit', params)
    },
    dels (params) {
      return post('/speechnotice/dels', params)
    }
  }
}
export default meetSpeech
