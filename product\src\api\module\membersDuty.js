// 导入封装的方法
import {
  post,
  postform,
  exportFile
} from '../http'

const membersDuty = {
  // 委员会客厅会客管理列表
  memberReceptionList (params) {
    return post('/memberReception/list?', params)
  },
  // 委员会客厅会客管理删除
  memberReceptionDels (params) {
    return post('/memberReception/dels?', params)
  },
  // 委员会客厅会客管理详情
  memberReceptionInfo (params) {
    return post(`/memberReception/info/${params}`)
  },
  // 委员会客厅会客列表
  memberReceptionListPc (params) {
    return post('/memberReception/listPc?', params)
  },
  // 来信列表（留言列表）
  memberReceptionCommentList (params) {
    return post('/memberReceptionComment/list', params)
  },
  // 删除来信列表（留言列表）
  memberReceptionCommentDels (params) {
    return post('/memberReceptionComment/dels', params)
  },
  // 修改来信列表状态 （审核通过、审核不通过）
  batchReview (params) {
    return post('/memberReceptionComment/batchReview', params)
  },
  // 来信详情
  memberReceptionCommentInfo (params) {
    return post(`/memberReceptionComment/info/${params}`)
  },
  // 获取主题委员
  getThemeDetails (params) {
    return post('/memberReception/getThemeDetails', params)
  },

  // 代表值班的导出
  memberGuestExport (data) {
    return exportFile('/officeonlinetopic/export', data)
  },
  // 来信导出
  letterExport (data) {
    return exportFile('/officeonlineletter/export', data)
  },
  // 获取值班代表
  memberList (data) {
    return post(`/officeonlineschedule/info/${data}`)
  },
  // 导入值班excel
  memberListExport (data) {
    return postform('/officeonlinetopic/import', data)
  },
  // 委员值班列表
  officeonlinescheduleList (params) {
    return post('/officeonlineschedule/list?', params)
  },

  // 委员值班列表删除
  officeonlinescheduleDel (params) {
    return post('/officeonlineschedule/dels', params)
  },
  // 委员值班主题列表
  officeonlinetopicList (params) {
    return post('/officeonlinetopic/list?', params)
  },
  // 新增委员值班来信
  officeonlineletterAddByPc (params) {
    return post('/officeonlineletter/addByPc', params)
  },
  // 委员值班来信列表
  officeonlineletterList (params) {
    return post('/officeonlineletter/list?', params)
  },
  // 委员值班来信列表详情
  officeonlineletterInfo (params) {
    return post(`/officeonlineletter/info/${params}`)
  },
  // 委员值班来信审核
  officeonlineletterAudit (params) {
    return post('/officeonlineletter/publish', params)
  },
  // 委员值班来信删除
  officeonlineletterDel (params) {
    return post('/officeonlineletter/dels', params)
  },
  // 设置来信默认公开状态
  officeonlineletterEditSwitch (data) {
    return post('/officeonlineletter/editSwitch', data)
  },
  // 获取来信默认公开状态
  officeonlineletterShowSwitch (data) {
    return post('/officeonlineletter/showSwitch', data)
  },
  // 评论编辑
  commentSave (data) {
    return post('/comment/save', data)
  },
  // 分享+1
  shareSave (data) {
    return post('/share/save', data)
  },
  // 评论删除
  commentDelsMyComment (data) {
    return post('/comment/delsMyComment', data)
  },
  // 评论删除
  commentAudit (data) {
    return post('/comment/audit', data)
  }
}

export default membersDuty
