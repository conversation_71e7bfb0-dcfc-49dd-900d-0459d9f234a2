// 导入封装的方法
import { post, get } from '../http'
export default {
  // 网络议政管理
  manage: {
    treelist (params) { // 查询树列表
      return post('/tree/list?', params)
    },
    arealist (params) {
      return post('/area/tree', params)
    },
    list (params) {
      return post('/networkPolitics/list', params)
    },
    addedit (url, params) {
      return post(url, params)
    },
    add (params) {
      return post('/networkPolitics/addLegislationSurvey', params)
    },
    edit (params) {
      return post('/survey/edit', params)
    },
    info (id) {
      return get(`/networkPolitics/info/${id}`)
    },
    del (id) {
      return get(`/networkPolitics/del/${id}`)
    },
    dels (ids) {
      return post('/networkPolitics/dels', { ids: ids })
    }
  },
  // 议政建言
  advice: {
    list (params) {
      return post('/surveyNetworkPoliticsAdvice/list', params)
    },
    info (id) {
      return get(`/surveyNetworkPoliticsAdvice/info/${id}`)
    },
    verify (params) {
      return get('/surveyNetworkPoliticsAdvice/edit', params)
    },
    verifys (params) {
      return post('/surveyNetworkPoliticsAdvice/updStatus', params)
    },
    del (id) {
      return get(`/surveyNetworkPoliticsAdvice/del/${id}`)
    },
    dels (ids) {
      return post('/surveyNetworkPoliticsAdvice/dels', { ids: ids })
    }
  },
  // 议政建言
  uploadData: {
    list (params) {
      return post('/surveyNetworkPoliticsData/list', params)
    },
    info (id) {
      return get(`/surveyNetworkPoliticsData/info/${id}`)
    },
    edit (params) {
      return get('/surveyNetworkPoliticsData/edit', params)
    },
    del (id) {
      return get(`/surveyNetworkPoliticsData/del/${id}`)
    },
    dels (ids) {
      return post('/surveyNetworkPoliticsData/dels', { ids: ids })
    },
    add (params) {
      return post('/surveyNetworkPoliticsData/add', params)
    }
  }
}
