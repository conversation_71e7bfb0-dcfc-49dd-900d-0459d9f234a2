// 导入封装的方法
import { post, get } from '../http'

const supervision = {
  generalAdd (url, params) {
    return post(url, params)
  },
  superviseProjectList (params) { // 民生实事监督项目列表
    return get('/superviseProject/list?', params)
  },
  superviseProjectInfo (params) { // 民生实事监督项目详情
    return get(`/superviseProject/info/${params}`)
  },
  superviseProjectDel (params) { // 民生实事监督项目删除
    return post('/superviseProject/deletes?', params)
  },
  superviseProjectSure (params) { // 民生实事监督项目删除
    return post('/superviseProject/sure?', params)
  },
  superviseProjectCopy (params) { // 民生实事监督项目复制
    return post('/superviseProject/copy?', params)
  },
  supervisePepoleFileList (params) { // 民生实事监督相关文件列表
    return get('/supervisePepoleFile/list?', params)
  },
  supervisePepoleFileInfo (params) { // 民生实事监督相关文件详情
    return get(`/supervisePepoleFile/info/${params}`)
  },
  supervisePepoleFileDel (params) { // 民生实事监督相关文件删除
    return post('/supervisePepoleFile/deletes?', params)
  },
  superviseTransactionList (params) { // 民生实事监督事项列表
    return get('/superviseTransaction/list?', params)
  },
  superviseTransactionDetail (params) { // 民生实事监督事项详情
    return get('/superviseTransaction/detail?', params)
  },
  supervisePepoleFileSatisfaction (params) { // 民生实事监督事项满意度
    return get('/superviseTransaction/satisfaction?', params)
  },
  supervisePepoleGroupList (params) { // 民生实事监督小组列表
    return get('/supervisePepoleGroup/list?', params)
  },
  supervisePepoleGroupInfo (params) { // 民生实事监督小组详情
    return get(`/supervisePepoleGroup/info/${params}`)
  },
  supervisePepoleGroupDel (params) { // 民生实事监督小组删除
    return post('/supervisePepoleGroup/deletes?', params)
  },
  superviseProjectSelect (params) { // 民生实事监督项目下拉选
    return get('/superviseProject/select?', params)
  },
  supervisePepoleActivityList (params) { // 民生实事工作开展情况列表
    return get('/supervisePepoleActivity/list?', params)
  },
  supervisePepoleActivityInfo (params) { // 民生实事工作开展情况详情
    return get(`/supervisePepoleActivity/info/${params}`)
  },
  supervisePepoleActivityDel (params) { // 民生实事工作开展情况删除
    return post('/supervisePepoleActivity/deletes?', params)
  },
  superviseDocumentList (params) { // 民生实事监督相关材料情况列表
    return get('/superviseDocument/list?', params)
  },
  superviseDocumentInfo (params) { // 民生实事监督相关材料情况详情
    return get(`/superviseDocument/info/${params}`)
  },
  superviseDocumentDel (params) { // 民生实事监督相关材料情况删除
    return post('/superviseDocument/deletes?', params)
  },
  supervisePepoleReportList (params) { // 民生实事监督成果列表
    return get('/supervisePepoleReport/list?', params)
  },
  supervisePepoleReportInfo (params) { // 民生实事监督成果详情
    return get(`/supervisePepoleReport/info/${params}`)
  },
  supervisePepoleReportDel (params) { // 民生实事监督成果删除
    return post('/supervisePepoleReport/deletes?', params)
  },
  supervisePepoleMeetingFileList (params) { // 民生实事上会材料列表
    return get('/supervisePepoleMeetingFile/list?', params)
  },
  supervisePepoleMeetingFileInfo (params) { // 民生实事上会材料详情
    return get(`/supervisePepoleMeetingFile/info/${params}`)
  },
  supervisePepoleMeetingFileDel (params) { // 民生实事上会材料删除
    return post('/supervisePepoleMeetingFile/deletes?', params)
  },
  supervisePepoleSatisfactionList (params) { // 民生实事提案办理评价列表
    return get('/supervisePepoleSatisfaction/list?', params)
  },
  supervisePepoleSatisfactionInfo (params) { // 民生实事提案办理评价详情
    return get(`/supervisePepoleSatisfaction/info/${params}`)
  },
  supervisePepoleSatisfactionDel (params) { // 民生实事提案办理评价删除
    return post('/supervisePepoleSatisfaction/deletes?', params)
  },
  supervisePepoleCountAnalyse (params) { // 民生监督事项统计分析
    return get('/supervisePepoleCount/analyse?', params)
  },
  supervisePepoleGroupProjects (params) { // 查询民生监督小组已关连的事项
    return get('/supervisePepoleGroup/projects?', params)
  },
  supervisePepoleGroupSaveProjects (params) { // 保存常委会监督事项
    return get('/supervisePepoleGroup/save/projects?', params)
  },
  superviseDiscussClueList (params) { // 常委会监督---议题征集列表
    return get('/superviseDiscussClue/list?', params)
  },
  superviseDiscussClueInfo (params) { // 常委会监督---议题征集详情
    return get(`/superviseDiscussClue/info/${params}`)
  },
  superviseDiscussClueDel (params) { // 常委会监督---议题征集删除
    return post('/superviseDiscussClue/deletes?', params)
  },
  superviseDiscussList (params) { // 常委会监督---议题管理列表
    return get('/superviseDiscuss/list?', params)
  },
  superviseDiscussInfo (params) { // 常委会监督---议题管理详情
    return get(`/superviseDiscuss/info/${params}`)
  },
  superviseDiscussDel (params) { // 常委会监督---议题管理删除
    return post('/superviseDiscuss/deletes?', params)
  },
  superviseDiscussFileList (params) { // 常委会监督---相关文件列表
    return get('/superviseDiscussFile/list?', params)
  },
  superviseDiscussFileInfo (params) { // 常委会监督---相关文件详情
    return get(`/superviseDiscussFile/info/${params}`)
  },
  superviseDiscussFileDel (params) { // 常委会监督---相关文件删除
    return post('/superviseDiscussFile/deletes?', params)
  },
  superviseDiscussGroupList (params) { // 常委会监督---监督小组列表
    return get('/superviseDiscussGroup/list?', params)
  },
  superviseDiscussGroupInfo (params) { // 常委会监督---监督小组详情
    return get(`/superviseDiscussGroup/info/${params}`)
  },
  superviseDiscussGroupDel (params) { // 常委会监督---监督小组删除
    return post('/superviseDiscussGroup/deletes?', params)
  },
  superviseDiscussActivityList (params) { // 常委会监督---活动开展列表
    return get('/superviseDiscussActivity/list?', params)
  },
  superviseDiscussActivityInfo (params) { // 常委会监督---活动开展详情
    return get(`/superviseDiscussActivity/info/${params}`)
  },
  superviseDiscussActivityDel (params) { // 常委会监督---活动开展删除
    return post('/superviseDiscussActivity/deletes?', params)
  },
  superviseDiscussReportList (params) { // 常委会监督---报告管理列表
    return get('/superviseDiscussReport/list?', params)
  },
  superviseDiscussReportInfo (params) { // 常委会监督---报告管理详情
    return get(`/superviseDiscussReport/info/${params}`)
  },
  superviseDiscussReportDel (params) { // 常委会监督---报告管理删除
    return post('/superviseDiscussReport/deletes?', params)
  },
  superviseAuditFileList (params) { // 常委会监督---审议意见列表
    return get('/superviseAuditFile/list?', params)
  },
  superviseAuditFileInfo (params) { // 常委会监督---审议意见详情
    return get(`/superviseAuditFile/info/${params}`)
  },
  superviseAuditFileDel (params) { // 常委会监督---审议意见删除
    return post('/superviseAuditFile/deletes?', params)
  },
  superviseAbarbeitungList (params) { // 常委会监督---整改反馈列表
    return get('/superviseAbarbeitung/list?', params)
  },
  superviseAbarbeitungInfo (params) { // 常委会监督---整改反馈详情
    return get(`/superviseAbarbeitung/info/${params}`)
  },
  superviseAbarbeitungDel (params) { // 常委会监督---整改反馈删除
    return post('/superviseAbarbeitung/deletes?', params)
  },
  superviseDiscussSelect (params) { // 常委会监督---议题下拉选
    return get('/superviseDiscuss/select', params)
  },
  superviseDiscussCountAnalyse (params) { // 常委会监督事项统计分析
    return get('/superviseDiscussCount/analyse?', params)
  },
  superviseDiscussGroupDiscusses (params) { // 查询民生监督小组已关连的事项
    return get('/superviseDiscussGroup/discusses?', params)
  },
  superviseDiscussGroupSaveDiscusses (params) { // 保存常委会监督事项
    return get('/superviseDiscussGroup/save/discusses?', params)
  }
}

export default supervision
