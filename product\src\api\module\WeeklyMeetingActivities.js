import { post } from '../http'

const WeeklyMeetingActivities = {

  // 每周会议活动列表
  Weeklylist (params) {
    return post('/weeklyactivity/list', params)
  },
  // 每周会议活动新增
  Weeklyadd (url, params) {
    return post(url, params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  // 每周会议活动修改
  Weeklyedit (url, params) {
    return post(url, params)
  },
  // 每周会议活动详情
  WeeklyInfo (params) {
    return post(`/weeklyactivity/info/${params}`)
  },
  // 每周会议活动详情
  WeeklyGetLogInfo (params) {
    return post(`/conferenceActivities/getLogInfo/${params}`)
  },
  // 每周会议活动删除
  WeeklyDel (params) {
    return post(`/weeklyactivity/del/${params}`)
  },
  // 每周会议活动删除
  WeeklyDels (params) {
    return post('/weeklyactivity/dels', params)
  }
}

export default WeeklyMeetingActivities
