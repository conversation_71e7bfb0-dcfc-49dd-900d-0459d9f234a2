// 导入封装的方法
import { post, get } from '../http'
import Vue from 'vue'
// 备案管理
const recordManage = {
  recordingNotReceivedList (params) { // 未接收列表
    return post('/file/recording/recordingNotReceivedList?', params)
  },
  recordingNotDistributedList (params) { // 未分发列表
    return post('/file/recording/recordingNotDistributedList?', params)
  },
  recordingDistributedList (params) { // 已分发列表
    return post('/file/recording/recordingDistributedList?', params)
  },
  recordingNoDistributedList (params) { // 无需分发列表
    return post('/file/recording/recordingNoDistributedList?', params)
  },
  recordingApplyForTakeBackList (params) { // 申请收回列表
    return post('/file/recording/recordingApplyForTakeBackList?', params)
  },
  recordingReBackList (params) { // 已退回列表
    return post('/file/recording/recordingReBackList?', params)
  },
  notPaperReceiptList (params) { // 纸质件未接收列表
    return post('/file/recording/notPaperReceiptList?', params)
  },
  paperReceiptList (params) { // 纸质件已接收列表
    return post('/file/recording/paperReceiptList?', params)
  },
  agreeApplyForTakeBack (params) { // 同意收回
    return post('/file/recording/agreeApplyForTakeBack', params)
  },
  updatePaperReceipt (params) { // 接收纸质件
    return post('/file/recording/updatePaperReceipt', params)
  },
  notAcceptedSuggestlist (params) { // 未受理审查建议
    return post('/filerecordingsuggest/notAcceptedSuggestlist', params)
  },
  notDistributeSuggestlist (params) { // 未分发审查建议
    return post('/filerecordingsuggest/notDistributeSuggestlist', params)
  },
  distributeSuggestlist (params) { // 已分发审查建议
    return post('/filerecordingsuggest/distributeSuggestlist', params)
  },
  reBackSuggestlist (params) { // 已退回审查建议
    return post('/filerecordingsuggest/reBackSuggestlist', params)
  },
  reasonSuggestlist (params) { // 已回复审查建议
    return post('/filerecordingsuggest/reasonSuggestlist', params)
  },
  suggestAccept (params) { // 审查建议受理
    return post('/filerecordingsuggest/suggestAccept', params)
  },
  filerecordingsuggestInfo (params) { // 审查建议详情
    return post(`/filerecordingsuggest/info/${params}`)
  },
  recordnormativeInfo (params) { // 规范性文件库详情
    return post(`/file/recording/record/normative/info/${params}`)
  },
  filerecordingnormativeInfo (params) { // 规范性文件库详情
    return post(`/filerecordingnormative/info/${params}`)
  },
  filerecordingnormativeDel (params) { // 规范性文件库详情
    return post('/filerecordingnormative/dels', params)
  },
  reviewCheckInfo (params) { // 审查管理 - 审查详情
    return post(`/recording/review/checkInfo/${params}`)
  },
  notReplyList (params) { // 未回复列表
    return post('/recording/review/notReplyList?', params)
  },
  noFeedbackList (params) { // 未反馈列表
    return post('/recording/review/noFeedbackList?', params)
  },
  agreeModifyList (params) { // 同意修改列表
    return post('/recording/review/agreeModifyList?', params)
  },
  disagreeModifyList (params) { // 不同意列表
    return post('/recording/review/disagreeModifyList?', params)
  },
  archiveList (params) { // 存档备查
    return post('/recording/review/archiveList?', params)
  },
  revokeList (params) { // 已撤销
    return post('/recording/review/revokeList?', params)
  },
  myFeedbackList (params) { // 审查意见反馈列表
    return post('/recording/review/myFeedbackList?', params)
  },
  yearRecordingList (params) { // 年度备案文件查询
    return post('/file/recording/yearRecordingList?', params)
  },
  sendYearRecording (params) { // 年度备案目录报送
    return post('/file/recording/sendYearRecording', params)
  },
  myRecordingList (params) { // 备案审查查询 -备案
    return post('/file/recording/myRecordingList?', params)
  },
  allSuggestlist (params) { // 备案审查查询 - 审查建议列表
    return post('/filerecordingsuggest/allSuggestlist?', params)
  },
  myReviewList (params) { // 备案审查查询 - 审查意见回复列表
    return post('/recording/review/myReviewList?', params)
  },
  allNormativeList (params) { // 备案文件查询
    return post('/file/recording/allNormativeList?', params)
  },
  annotationList (params) { // 智慧辅助-智能审查列表
    return post('/recording/intelligent/annotation/list', params)
  },
  annotationDel (params) { // 智慧辅助-智能审查列表删除
    return post('/recording/intelligent/annotation/dels', params)
  },
  reportCLose (params) { // 报备关闭
    return post('/file/recording/reportCLose', params)
  },
  recordingUpdateFile (params) { // 审查意见反馈修改文件
    return post('/file/recording/recordingUpdateFile', params)
  },
  annotationInfo (params) { // 智慧辅助-智能审查详情
    return post(`/recording/intelligent/annotation/info/${params}`)
  },
  recordingAnnotationList (params) { // 批注列表
    return post('/recording/annotation/list', params)
  },
  recordingAnnotationDel (params) { // 批注列表
    return post('/recording/annotation/dels', params)
  },
  staticsNormative (params) { // 驾驶舱 - 规范性文件统计
    return post('/record/statics/normative', params)
  },
  staticsNormativeColumnar (params) { // 驾驶舱 - 规范性文件走势
    return post('/record/statics/normative/columnar', params)
  },
  staticsReview (params) { // 驾驶舱 - 审查统计
    return post('/record/statics/review', params)
  },
  staticsSuggest (params) { // 驾驶舱 - 审查建议统计
    return post('/record/statics/suggest', params)
  },
  getUnderArea (params) { // 驾驶舱 - 审查建议统计
    return post('/area/getUnderArea', params)
  },
  recordResultEditInfo (id, params) { // 审查结果
    return post(`/recording/reason/recordResultEditInfo/${id}`, params)
  },
  recordFeedBackEditInfo (params) { // 审查反馈
    return post(`/recording/reason/recordFeedBackEditInfo/${params}`)
  },
  recordingGroupTree (params) { // 获取报备单位树
    return post('/recording/group/tree', params)
  },
  findDetail (params) { // 获取报备单位树
    return post('/recording/intelligent/annotation/findDetail', params)
  },
  fileRecordingList (params) { // 报备相关
    return post('/file/recording/list', params)
  },
  filerecordingnormativeList (params) { // 备案
    return post('/filerecordingnormative/list', params)
  },
  filerecordingsuggestAllList (params) { // 建议相关
    return post('/filerecordingsuggest/allList', params)
  },
  filerecordingsuggestList (params) { // 建议相关
    return post('/filerecordingsuggest/List', params)
  }
}
const RecordingReview = {
  generalAdd (url, params) {
    return post(url, params)
  },
  keywordList (params) { // 获取关键字列表
    return post('/recording/keyword/list?', params)
  },
  keywordDel (params) { // 获取关键字列表删除
    return post('/recording/keyword/dels', params)
  },
  keywordBatch (type, params) { // 获取关键字列表启用禁用
    return post('/recording/keyword/batch/' + type, params)
  },
  keywordInfo (params) { // 获取关键字列表启用禁用
    return post(`/recording/keyword/info/${params}`)
  },
  groupList (params) { // 获取报备单位列表
    return post('/recording/group/list?', params)
  },
  groupDel (params) { // 获取报备单位列表删除
    return post('/recording/group/dels', params)
  },
  groupBatch (type, params) { // 获取报备单位列表启用禁用
    return post('/recording/group/batch/' + type, params)
  },
  groupInfo (params) { // 获取报备单位列表启用禁用
    return post(`/recording/group/info/${params}`)
  },
  groupUserList (params) { // 获取报备单位列表启用禁用
    return post('/recording/group/userList', params)
  },
  reportUnsubmitList (params) { // 未提交列表
    return post('/file/recording/reportUnsubmitList?', params)
  },
  reportAddedList (params) { // 待补充列表
    return post('/file/recording/reportAddedList?', params)
  },
  reportNotReceivedList (params) { // 未接收列表
    return post('/file/recording/reportNotReceivedList?', params)
  },
  reportReceivedList (params) { // 已接收列表
    return post('/file/recording/reportReceivedList?', params)
  },
  reportApplyBackList (params) { // 申请退回列表
    return post('/file/recording/reportApplyBackList?', params)
  },
  reportCloseList (params) { // 已关闭
    return post('/file/recording/reportCloseList?', params)
  },
  normativeList (params) { // 废止文件
    return post('/file/recording/normativeList?', params)
  },
  statuteLawList (params) { // 法规列表
    return post('/statute/law/list', params)
  },
  recordingInfo (params) { // 报备详情
    return post(`/file/recording/info/${params}`)
  },
  recordingDel (params) { // 报备删除
    return post('/file/recording/dels', params)
  },
  reportSubmit (params) { // 报备提交
    return post('/file/recording/reportSubmit', params)
  },
  reportTakeBack (params) { // 报备收回
    return post('/file/recording/reportTakeBack', params)
  },
  reportApplyForTakeBack (params) { // 报备申请收回
    return post('/file/recording/reportApplyForTakeBack', params)
  },
  myReportList (params) { // 报备查询
    return post('/file/recording/myReportList?', params)
  },
  myNormativeList (params) { // 报备文件查询
    return post('/file/recording/myNormativeList?', params)
  },
  recordReasonInfo (params) { // 审查意见详情
    return post(`/recording/reason/recordReasonInfo/${params}`)
  },
  reviewList (params) { // 获取审查管理列表
    return post('/recording/review/list', params)
  },
  reviewAllList (params) { // 获取ALL审查管理列表
    return post('/recording/review/allList', params)
  },
  reviewEdit (params) { // 获取审查管理列表提交
    return post('/recording/reason/edit', params)
  },
  reviewDels (params) { // 获取审查管理列表删除
    return post('/recording/review/dels', params)
  },
  dataFileNormdocHotList (params) { // 规范性文件列表查询
    return post('/filerecordingnormative/hotList', params)
  },
  dataFileNormdocAreas (params) { // 获取审查管理地区
    return post('/filerecordingnormative/normative/areas', params)
  },
  dataFileNormdocJoinFileList (params) { // 获取关联法规列表
    return post('/recording/join/file/list', params)
  },
  dataFileNormdocList (params) {
    // 获取规范性列表数据
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/normdoc/list`, params)
  },
  dataFileNormdocHotread (params) {
    // 获取热门阅读大数据
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/normdoc/hotread`, params)
  },
  dataFileNormdocInfo (params) {
    // 获取规范性文件详情
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/normdoc/info`, params)
  },
  dataFileNormdocFileType (params) {
    // 获取规范性文件类型
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/normdoc/fileType`, params)
  },
  dataFileNormdocRecommend (params) {
    // 获取相关规范性文件
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/normdoc/recommend`, params)
  },
  statuteRecordList (params) { // 备案审查资料搜索推荐
    return post('/statute/record/list', params)
  },
  recordingfavoriteList (params) { // 备案审查资料搜索推荐
    return post('/recording/favorite/list', params)
  },
  recordingFavoriteAdd (params) { // 备案审查资料收藏
    return post('/recording/favorite/add', params)
  },
  recordingFavoriteDel (params) { // 备案审查资料取消收藏
    return post('/recording/favorite/dels', params)
  },
  validityRank (params) { // 备案审查资料搜索推荐
    return post('/statute/validityRank', params)
  },
  regulationsList (params) { // 备案审查资料搜索推荐
    return post('/statute/regulations/list', params)
  },
  ...recordManage
}
export default RecordingReview
