<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <title>会议室布局</title>
  <!-- 引用css -->
  <link rel='stylesheet' href='./plugins/css/pluginsCss.css' />
  <link rel='stylesheet' href='./plugins/plugins.css' />
  <link rel='stylesheet' href='./css/luckysheet.css' />
  <link rel="stylesheet" href="./css/style.css">
  <link rel='stylesheet' href='./assets/iconfont/iconfont.css' />
  <link rel="stylesheet" href="./layui/css/layui.css">
  <script src="./plugins/js/plugin.js"></script>
  <script src="./luckysheet.umd.js"></script>
  <script src="./xlsx.full.min.js"></script>
</head>

<body>
  <div class="titleBox">
    <h2 id="roomName"></h2>
    <!-- <div id="zy" class="btn btn-info">操作指引</div> -->
  </div>
  <div class="titleBox radioBox">
    <div class="opt">
      <input class="magic-radio" type="radio" name="setUp" id="circular" value="1" checked>
      <label for="circular">座位展示区</label>
    </div>
    <div class="opt" style="margin-left: 25px;">
      <input class="magic-radio" type="radio" name="setUp" id="matrix" value="2">
      <label for="matrix">座位编辑区</label>
    </div>
  </div>
  <div class="titleBox btnBox">
    <div id="pictures" class="btn btn-info seat">生成图片</div>
    <div id="save" class="btn btn-info sheet">保存</div>
    <div id="download" class="btn btn-info sheet">下载</div>
    <div class="layui-form sheet">
      <select id="zone" lay-filter="zone">
        <option value="1">座位区</option>
        <option value="0">非座位区</option>
      </select>
    </div>
    <!-- <div class="txt sheet">当前</div>
    <div class="layui-form sheet">
      <select id="currentRow" lay-filter="currentRow">
      </select>
    </div>
    <div class="txt row sheet">排</div> -->
  </div>
  <div id="seatBox" style="width:100%;">
    <div class="titleBox radioBox boxBorder"></div>
  </div>
  <div id="test" style="display: none;width:600px;height:200px;">
    <div class="noticeInfo">保存失败</div>
    <div class="noticeList">

    </div>
  </div>
  <div id="luckysheet" style="width:100%;height: 80%;"></div>
  <div id="previewImage"></div>
  <div id="cloneDiv"></div>
  <div id="images" style="display: none;"></div>
  <script src="./axios/axios.min.js"></script>
  <script src="./axios/qs.min.js"></script>
  <script src="./layui/layui.js"></script>
  <script src="./createPic/html2canvas.js"></script>
  <script src="./createPic/moment.js"></script>
  <script src="./createPic/canvas2image.js"></script>
  <script src="./createPic/base64.js"></script>
  <script>

    // var token = sessionStorage.getItem('tokenhenanzxjava');
    // var areaId = sessionStorage.getItem("tokenhenanzxjava");
    var token = sessionStorage.getItem('token' + sessionStorage.getItem('theme'))
    var areaId = sessionStorage.getItem('areaId' + sessionStorage.getItem("theme"))
    var maxX = 0; // 座位容器内最大的x坐标
    var maxY = 0; // 座位容器内最大的y坐标
    var maxImgX = 0; // 缩略图容器内最大的x坐标
    var maxImgY = 0; // 缩略图容器内最大的y坐标
    var selSheet = ''; // 选中的sheet表格
    var showArea = '1'; // 1：展示区 2：编辑区
    var urlName = document.location.hostname;
    var httpUrl = sessionStorage.getItem('switchpage' + sessionStorage.getItem('theme')).replace("\"","").replace("\"","");
    var placeId = '';
    var infoId = '';
    var seatData = '';
    var seatDataBack = '';

    $(function () {
      // switch (urlName) {
      //   case "lzpt.hnzx.gov.cn":
      //     httpUrl = "http://lzpt.hnzx.gov.cn/platform-lzt";
      //     break;
      //   case "***********":
      //     httpUrl = "http://***********:20116/lzt";
      //     break;
      //   case "**************":
      //     httpUrl = "http://**************:22325/lzt";
      //     break;
      //   default:
      //     httpUrl = "http://**************:22325/lzt";
      //     break;
      // }
      // console.log(urlName, document.location.pathname)
      // if (urlName === 'lzpt.hnzx.gov.cn') {
      //   httpUrl = 'http://lzpt.hnzx.gov.cn/platform-lzt'
      // } else if (urlName === '**************') {
      //   httpUrl = "http://**************:22325/lzt"
      // } else if (urlName === '***********' && document.location.pathname === '/zyrd-platform/meetingSeats/meetingSeats.html') {
      //   httpUrl = 'http://***********:20114/lzt'
      // } else if (urlName === '***********' && document.location.pathname === '/zyzx-platform/meetingSeats/meetingSeats.html') {
      //   httpUrl = 'http://***********:20116/lzt'
      // }
      placeId = window.sessionStorage.getItem('meetPlaceId');
      $("#roomName").text(sessionStorage.getItem('meetPlaceName'));
      $("#seatBox").css("height", $("body").height() - 140);
      showBox(showArea); // 切换座位展示区/座位编辑区
      loadCurrentRowList(); // 加载当前排数下拉框数据
    })

    // 加载表格组件
    function loadSheet () {
      var loadUrl = seatData; // 原始座位信息
      // 配置项
      var options = {
        container: 'luckysheet' // luckysheet为容器id
      }
      luckysheet.create({
        container: 'luckysheet', // 设定DOM容器的id
        title: this.title, // 设定表格名称
        lang: 'zh', // 设定表格语言
        plugins: ['chart'],
        enableAddRow: false,
        enableAddBackTop: false,
        showsheetbar: true,
        showtoolbarConfig: {
          currencyFormat: false,
          percentageFormat: false, // 百分比格式
          numberDecrease: false, // '减少小数位数'
          numberIncrease: false, // '增加小数位数
          moreFormats: false, // '更多格式'
          image: false, // '插入图片'
          link: false, // '插入链接'
          horizontalAlignMode: true, // '水平对齐方式'
          textWrapMode: false,
          chart: false,
          pivotTable: false, // '数据透视表'
          postil: false,
          function: false, // '公式'
          frozenMode: false, // '冻结方式'
          conditionalFormat: false // '条件格式'
        },
        hook: {
          cellMousedown: cellClick, // 绑定鼠标事件
        },
        data: loadUrl ? loadUrl : initData()
      })
    }

    function initData () {
      const data = [
        {
          name: 'Sheet1', // 工作表名称
          color: '', // 工作表颜色
          index: 0, // 工作表索引
          status: 1, // 激活状态
          order: 0, // 工作表的下标
          hide: 0, // 是否隐藏
          row: 36, // 行数
          column: 30, // 列数
          defaultRowHeight: 19, // 自定义行高
          defaultColWidth: 73, // 自定义列宽
          celldata: [], // 初始化使用的单元格数据
          config: {
            merge: {}, // 合并单元格
            rowlen: {}, // 表格行高
            columnlen: {}, // 表格列宽
            rowhidden: {}, // 隐藏行
            colhidden: {}, // 隐藏列
            borderInfo: {}, // 边框
            authority: {} // 工作表保护
          },
          scrollLeft: 0, // 左右滚动条位置
          scrollTop: 0, // 上下滚动条位置
          luckysheet_select_save: [], // 选中的区域
          calcChain: [], // 公式链
          isPivotTable: false, // 是否数据透视表
          pivotTable: {}, // 数据透视表设置
          filter_select: {}, // 筛选范围
          filter: null, // 筛选配置
          luckysheet_alternateformat_save: [], // 交替颜色
          luckysheet_alternateformat_save_modelCustom: [], // 自定义交替颜色
          luckysheet_conditionformat_save: {}, // 条件格式
          frozen: {}, // 冻结行列配置
          chart: [], // 图表配置
          zoomRatio: 1, // 缩放比例
          image: [], // 图片
          showGridLines: 1, // 是否显示网格线
          dataVerification: {} // 数据验证配置
        }
      ]
      return data
    }

    // 单元格点击
    function cellClick (item) {
      $('#zone').val(item === null ? '0' : item.zone);
      $('#currentRow').val(item === null ? '' : item.currentRow);
      layui.form.render();
    }

    layui.use(['form'], function () {
      var form = layui.form;
      // 下拉框数据加载
      var option = '';
      for (var i = 1; i <= 100; i++) {
        option += '<option value="' + i + '">' + i + '</option>';
        $("#currentRow").html(option);
      }
      form.render('select');

      // 下拉框change事件
      form.on('select(zone)', function (data) {
        var val = data.value;
        console.info(val);
        setVal('zone', val);
      });

      form.on('select(currentRow)', function (data) {
        var val = data.value;
        console.info(val);
        setVal('currentRow', val);
      });
    });

    function setVal (key, val) {
      const getRange = luckysheet.getRange()
      const getRangeValue = luckysheet.getRangeValue()
      const arr = []
      if (getRangeValue.length) {
        getRangeValue.map(i => {
          if (i.length) {
            const item = i.map(j => {
              if (j) {
                j[key] = val
                if (key === 'currentRow') {
                  const m = j.m ? j.m : ''
                  // j.m = m
                  // j.v = j.m
                }
                if (key === 'zone') {
                  j.zone = val
                }
              } else {
                j = { [key]: val }
                if (key === 'currentRow') {
                  // j.m = val
                  // j.v = val
                }
                if (key === 'zone') {
                  j.zone = val
                  j.zone = val
                }
              }
              return j
            })
            arr.push(item)
          }
        })
      }
      luckysheet.setRangeValue(arr, getRange);
      luckysheet.refresh();
    }

    //加载当前排数下拉框数据
    function loadCurrentRowList () {
      $("#currentRow").empty();
      var addStr = '';
      for (var i = 1; i <= 100; i++) {
        addStr = addStr + '<option value="' + i + '">' + i + '</option>';
      }
      $("#currentRow").append(addStr);
    }

    //单选按钮点击事件
    $("input:radio[name='setUp']").on('click', function (event) {
      showArea = $(this).val();
      showBox(showArea);
    });

    // 多表数据切换
    $(document).on('click', '.sheetTable', function () {
      selSheet = $(this).val();
      loadSeatsBySheetName(selSheet); // 根据sheetName加载座位
    });

    //切换座位展示区/座位编辑区
    function showBox () {
      // 加载座位数据
      axios.post(httpUrl + "/meetPlace/getLayout", Qs.stringify({
        placeId: placeId
      }), {
        headers: {
          "Authorization": token,
          "u-login-areaId": areaId
        }
      })
        .then(function (response) {
          var resultInfo = response.data;
          if (resultInfo.errcode == 200) {
            infoId = resultInfo.data.id;
            seatData = resultInfo.data.layout === '' ? null : JSON.parse(resultInfo.data.layout)
            console.log(seatData)
            seatDataBack = resultInfo.data.layoutBackup === '' ? null : JSON.parse(resultInfo.data.layoutBackup)
            console.log(seatDataBack)
            if (showArea == "1") {
              showSeat(); // 座位信息展示
              $("#luckysheet, .sheet").hide();
              $("#seatBox, .seat, #previewImage").show();
            } else {
              loadSheet(); // 加载表格组件
              $("#luckysheet, .sheet").show();
              $("#seatBox, .seat, #previewImage").hide();
            }
          }
          else {
            layer.msg(resultInfo.errmsg);
          }
        }).catch(function (error) {
          layer.msg(error.response.data.message);
        });
    }

    // 保存
    $("#save").on("click", function () {
      var table = luckysheet.getAllSheets();
      console.log(table); // 表格原始数据
      window.sessionStorage.setItem('sheetInfo', JSON.stringify(table));
      format(table); // 重设数组
    });

    // 重设数组
    function format (data) {
      if (data.length) {
        // 获取数组的data
        const arr = []
        data.forEach((i) => arr.push(i.data))
        console.log(arr) // 将表格数据取出来放入数组
        const item = []
        let row = 1
        const table = 0 // 第一个表格
        for (let i = 0; i < arr.length; i++) { // 循环每一个表格
          row = table === i ? row : 1
          for (let j = 0; j < arr[i].length; j++) { // 循环每一行
            let g = 0 // 记录每一行循环到了哪一个格子
            let empty = true // 记录行是否为空
            for (let k = 0; k < arr[i][j].length; k++) { // 循环每一行的每一列（每一个格子）
              g++
              if (g === arr[i][j].length) { // 循环到最后一个格子，一行结束
                if (empty === false) {
                  row++
                }
              }
              const rowItem = arr[i][j][k]
              if (rowItem !== null) { // 只要有一个格子为不空，整行就不为空
                empty = false
                const object = {
                  location: j + ',' + k, // row + ',' + (k + 1),  格式化后的几排几座
                  oldLocation: j + ',' + k, // 原始数据的几排几座
                  zone: rowItem.zone, // 1座位区 0非座位区
                  currentRow: rowItem.currentRow, // 当前第几排
                  v: rowItem.v === undefined ? (rowItem.ct === null ? '' : rowItem.v) : rowItem.v, // 原始值
                  m: rowItem.m === undefined ? (rowItem.ct === null ? '' : rowItem.m) : rowItem.m, // 显示值
                  mc: rowItem.mc, // 合并单元格必备属性 mc.r:主单元格的行号 mc.c:主单元格的列号 mc.rs:合并单元格占的行数 mc.cs:合并单元格占的列数
                  table: data[i].name, // 所属表格名称
                  tableIndex: i, // 所属表格下标
                  bg: rowItem.bg, // 单元格背景颜色
                  fc: rowItem.fc, // 单元格字体颜色
                  fs: rowItem.fs, // 单元格字体大小
                  ff: rowItem.ff, // 单元格字体
                  bl: rowItem.bl, // 单元格字体加粗
                  it: rowItem.it, // 单元格字体斜体
                  cl: rowItem.cl, // 单元格字体删除线
                  un: rowItem.un // 单元格字体下划线
                }
                item.push(object)
              }
            }
          }
        }
        console.log(item) //封装好的表格数据
        window.sessionStorage.setItem('seatInfo', JSON.stringify(item));
        axios.post(httpUrl + "/meetPlace/saveLayout", Qs.stringify({
          id: infoId,
          placeId: placeId,
          layout: JSON.stringify(data),
          layoutBackup: JSON.stringify(item)
        }), {
          headers: {
            "Authorization": token,
            "u-login-areaId": areaId
          }
        })
          .then(function (response) {
            var resultInfo = response.data;
            if (resultInfo.errcode == 200) {
              layer.msg("保存成功");
            }
            else {
              var noticeList = response.data.data.placeLayoutInfoList
              if (noticeList.length > 0) {
                $(".noticeList").empty()
                for (var i = 0; i < noticeList.length; i++) {
                  $(".noticeList").append('<p style="padding: 10px 20px; font-size: 15px; color: #333;">' + noticeList[i].table + ' ' + noticeList[i].v + '信息重复</p>')
                }
              }
              layui.use('layer', function () {
                var layer = layui.layer;
                layer.open({
                  title: '提示信息',
                  type: 1,
                  content: $("#test"),
                  btn: ['确定'],
                  yes: function (index, layero) {
                    layer.close(index)
                  }
                })
              })
            }
          }).catch(function (error) {
            layer.msg(error.response.data.message);
          });
      }
    }

    // 座位信息展示
    function showSeat () {
      var sheetArr = seatData; // 原始座位信息
      if (sheetArr !== null && sheetArr.length > 0) {
        $(".boxBorder").empty();
        for (var i = 0; i < sheetArr.length; i++) {
          var sheetName = sheetArr[i].name; // 表格名称
          var addStr = "";
          if (i === 0) {
            addStr = '<div class="optBtn"><input class="magic-radio sheetTable" type="radio" name="tableName" id="' + sheetName + '" value="' + sheetName + '" checked><label for="' + sheetName + '">' + sheetName + '</label></div>';
          } else {
            addStr = '<div class="optBtn"><input class="magic-radio sheetTable" type="radio" name="tableName" id="' + sheetName + '" value="' + sheetName + '"><label for="' + sheetName + '">' + sheetName + '</label></div>'
          }
          $(".boxBorder").append(addStr);
        }
        var checkedSheet = $("input:radio[name='tableName']:checked").val();
        selSheet = checkedSheet;
        loadSeatsBySheetName(checkedSheet); // 根据sheetName加载座位
      }
    }

    // 根据sheetName加载座位
    function loadSeatsBySheetName (sheetName) {
      $("#seatBox, #previewImage").find(".seatItem").remove();
      var seatArr = seatDataBack; // 封装后座位信息
      if (seatArr != null && seatArr.length > 0) {
        for (var i = 0; i < seatArr.length; i++) {
          var location = seatArr[i].location; // 封装后的几排几座
          var m = seatArr[i].m === undefined || seatArr[i].m === null ? '' : seatArr[i].m; // 原始值
          var v = seatArr[i].v === undefined || seatArr[i].v === null ? '' : seatArr[i].v; // 显示值
          var mc = seatArr[i].mc; // 合并单元格必备属性 mc.r:主单元格的行号 mc.c:主单元格的列号 mc.rs:合并单元格占的行数 mc.cs:合并单元格占的列数
          var oldLocation = seatArr[i].oldLocation; // 原始数据的几排几座
          var tName = seatArr[i].table; // 所属表格名称
          var zone = seatArr[i].zone; // 1座位区 0非座位区
          var currentRow = seatArr[i].currentRow; // 当前第几排
          var bg = seatArr[i].bg === undefined || seatArr[i].bg === null ? "#00A0EA" : seatArr[i].bg; // 背景颜色
          var fc = seatArr[i].fc === undefined || seatArr[i].fc === null ? "#FFFFFF" : seatArr[i].fc; // 字体颜色
          var fs = seatArr[i].fs === undefined || seatArr[i].fs === null ? "12px" : seatArr[i].fs; // 字体大小
          var ff = seatArr[i].ff === undefined || seatArr[i].ff === null ? "" : seatArr[i].ff; // 字体
          var bl = seatArr[i].bl === undefined || seatArr[i].bl === null ? "" : seatArr[i].bl; // 字体加粗
          bl = bl === 0 || bl === '' ? 'normal' : 'bold'
          var it = seatArr[i].it === undefined || seatArr[i].it === null ? "" : seatArr[i].it; // 字体斜体
          it = it === 0 || it === '' ? 'normal' : 'italic'
          var cl = seatArr[i].cl === undefined || seatArr[i].cl === null ? "" : seatArr[i].cl; // 字体删除线
          cl = cl === 0 || cl === '' ? 'none' : 'line-through'
          var un = seatArr[i].un === undefined || seatArr[i].un === null ? "" : seatArr[i].un; // 字体下划线
          un = un === 0 || un === '' ? 'none' : 'underline'
          un = un === 'none' ? cl : un
          var h = mc !== undefined ? 60 * mc.rs + (mc.rs * 5 - 5) : 60; //座位高度
          var w = mc !== undefined ? 120 * mc.cs + (mc.cs * 5 - 5) : 120; //座位宽度
          var position = oldLocation.split(",");
          var y = position[0] * 60 + position[0] * 5 + 60; //座位y坐标
          var x = position[1] * 120 + position[1] * 5; //座位x坐标

          if (Number(x) > Number(maxX)) {
            maxX = x; //给容器内的最大x坐标赋值
          }
          if (Number(y) > Number(maxY)) {
            maxY = y; //给容器内的最大y坐标赋值
          }

          // 过滤掉显示值为空的座位
          if (v !== '' && tName === sheetName) {
            var addStr = '<div class="seatItem" style="z-index: 1;width:' + w + 'px;height:' + h + 'px;top:' + y + ';left:' + x + ';background:' + bg + ';font-size:' + fs + ';font-family:' + ff + '; font-weight:' + bl + ';font-style:' + it + ';text-decoration:' + un + ';">' +
              '<label style="color:' + fc + ';">' + v + '</label>' + //第一个label放显示值
              '<label style="display:none; color:black;">' + m + '</label>' + //第二个label放原始值
              '<label style="display:none; color:black;">' + location + '</label>' + //第三个label放封装后的几排几座
              '<label style="display:none; color:black;">' + oldLocation + '</label>' + //第四个label放原始数据的几排几座
              '<label style="display:none; color:black;">' + tName + '</label>' + //第五个label放所属表格名称
              '<label style="display:none; color:white;">' + zone + '</label>' + //第六个label放是否座位区 1座位区 0非座位区
              '<label style="display:none; color:black;">' + currentRow + '</label>' + //第七个label放当前第几排
              '<label style="display:none; color:black;">' + bg + '</label>' + //第八个label放背景颜色
              '</div>';
            $("#seatBox").append(addStr);

            // 加载缩略图数据
            var ph = mc !== undefined ? 2 * mc.rs + (mc.rs * 2 - 2) : 2; //座位高度
            var pw = 4; //座位宽度
            var pposition = oldLocation.split(",");
            var py = position[0] * 5 + position[0] * 2; //座位y坐标
            var px = position[1] * 4 + position[1] * 2; //座位x坐标
            if (Number(px) > Number(maxImgX)) {
              maxImgX = px; //给容器内的最大x坐标赋值
            }
            if (Number(py) > Number(maxImgY)) {
              maxImgY = py; //给容器内的最大y坐标赋值
            }
            console.log(px, py)
            var previewStr = '<div class="seatItem" style="z-index: 1;width:' + pw + 'px;height:' + ph + 'px;top:' + py + ';left:' + px + ';background:' + bg + ';"></div>';
            $("#previewImage").append(previewStr);
          }
        }
        $("#previewImage").css("width", maxImgX + 20 + 'px').css("height", maxImgY + 20 + 'px');
      }
    }

    // 生成图片
    $("#pictures").on("click", function () {
      if ($("#seatBox").find('.seatItem').length > 0) {
        //判断排座容器有没有出现滚动条，根据情况调整生成图片的宽高
        var bx = $("#seatBox");
        if (bx[0].scrollHeight > bx[0].clientHeight || bx[0].offsetHeight > bx[0].clientHeight) { //出现滚动条
          //获取节点高度，后面为克隆节点设置高度。
          var h = Number(maxY) + 200;
          var w = Number(maxX) + 200;
        } else { //未出现滚动条
          //获取节点高度，后面为克隆节点设置高度。
          var h = $("#seatBox").height();
          var w = $("#seatBox").width();
        }

        //克隆节点，默认为false，不复制方法属性，为true是全部复制。
        var cloneDom = $("#seatBox").clone(true);
        $("body").css("overflow", "hidden");
        //设置克隆节点的css属性，因为之前的层级为0，我们只需要比被克隆的节点层级低即可。
        cloneDom.css({
          "background-color": "white",
          "position": "absolute",
          "top": "5000px",
          "z-index": "-9",
          "height": h,
          "width": w
        });
        //将克隆节点动态追加到body后面。
        $("#cloneDiv").append(cloneDom);
        //插件生成base64img图片。
        html2canvas(cloneDom, {
          //Whether to allow cross-origin images to taint the canvas
          allowTaint: true,
          //Whether to test each image if it taints the canvas before drawing them
          taintTest: false,
          onrendered: function (canvas) {
            //添加属性
            canvas.setAttribute('id', 'thecanvas');
            //读取属性值
            // var value= canvas.getAttribute('id');
            document.getElementById('images').innerHTML = '';
            document.getElementById('images').appendChild(canvas);

            /*自动保存为png*/
            // 获取图片资源
            var oCanvas = document.getElementById("thecanvas");
            var img_data1 = Canvas2Image.saveAsPNG(oCanvas, true).getAttribute('src');
            var timeStamp = moment(new Date()).format("YYYYMMDDHHmmss");
            var picName = timeStamp + '.jpg'
            saveFile(img_data1, picName);
            $(":radio[name='tableName'][value='" + selSheet + "']").prop("checked", "checked");
          }
        });
      } else {
        layer.msg("当前没有座位需要生成图片");
      }
    });

    // 保存文件函数
    var saveFile = function (data, filename) {
      var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
      save_link.href = data;
      save_link.download = filename;
      var event = document.createEvent('MouseEvents');
      event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
      save_link.dispatchEvent(event);
      //生成并下载图片后，将克隆的元素清空
      document.getElementById('cloneDiv').innerHTML = '';
    };

    // 点击缩率图，座位展示区位移到相应位置
    $("#previewImage").on('click', function (e) {
      var x = e.offsetX
      var y = e.offsetY
      var w = $("#seatBox").width() - 10
      var h = $("#seatBox").height() - 10
      var xProportion = x / w
      var yProportion = y / h
      if (xProportion > 0.04) {
        $("#seatBox").scrollLeft($(this).width() * xProportion * 80)
      } else {
        $("#seatBox").scrollLeft(0)
      }
      if (yProportion > 0.04) {
        $("#seatBox").scrollTop($(this).height() * yProportion * 80)
      } else {
        $("#seatBox").scrollTop(0)
      }
    });

    // 下载
    $("#download").on('click', function () {
      var data = luckysheet.getAllSheets();
      var info = [];
      var name = '';
      for (var i = 0; i < data.length; i++) {
        if (data[i].status === 1) {
          info = data[i].data
          name = data[i].name
        }
      }
      var out = XLSX.utils.book_new(),
        aoa = [[]];
      info.forEach(function (rows, index) {
        aoa[index] = [];
        for (var column = 0; column < rows.length - 1; column++) {
          aoa[index][column] = rows[column] !== null ? rows[column] : '';
        }
      });
      var ws = XLSX.utils.aoa_to_sheet(aoa);
      XLSX.utils.book_append_sheet(out, ws, name);
      XLSX.writeFile(out, name + '.xlsx', {});
      return true;
    });
  </script>
</body>

</html>