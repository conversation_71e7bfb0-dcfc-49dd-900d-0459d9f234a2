import {
  get,
  post,
  postform,
  exportFile,
  fileRequest,
  filedownload
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
const memberInformation = {
  memberLists (params) {
    return post('/provincemember/list?', params)
  },
  memberList (params) {
    return post('/member/list?', params)
  },
  memberDel (params) {
    return post('/member/batch/del', params)
  },
  memberStartuse (params) {
    return post('/member/batch/startuse', params)
  },
  memberStopuse (params) {
    return post('/member/batch/stopuse', params)
  },
  memberResetpwd (params) {
    return post('/member/batch/resetpwd', params)
  },
  memberInfo (params) {
    return post(`/member/info/${params}`)
  },
  memberDetails (params) {
    return post('/member/look?', params)
  },
  historymemberList (params) {
    return post('/historymember/list?', params)
  },
  historymemberSelect (params) {
    return post('/historycircles/select?', params)
  },
  historymemberInfo (params) {
    return post(`/historymember/look/${params}`)
  },
  territory (params) {
    return post('/territory', params)
  },
  historycirclesList (params) {
    return post('/historycircles/list?', params)
  },
  historycirclesInfo (params) {
    return post(`/historycircles/info/${params}`)
  },
  historycirclesDel (params) {
    return post('/historycircles/dels?', params)
  },
  memberCount (params) {
    return post('/member/count?', params)
  },
  // 换届
  changejc (params) {
    return post('/member/changejc', params)
  },
  // 出缺
  batchOut (params) {
    return post('/member/batch/out', params)
  },
  // 暂停
  batchstop (params) {
    return post('/member/batch/stop', params)
  },
  // 取消出缺
  nobatchOut (params) {
    return post('/member/batch/nout', params)
  },
  // 下载模板
  importemplate (params) {
    exportFile('/member/importemplate', params)
  },
  // 导入代表或全国代表
  import (params, text) {
    fileRequest('/member/import', params, text)
  },
  // 代表信息审核
  userauditList (params) {
    return post('/useraudit/list?', params)
  },
  // 代表信息审核详情
  userauditInfo (params) {
    return post('/useraudit/info?', params)
  },
  // 代表信息审核删除
  userauditDels (params) {
    return post('/useraudit/dels?', params)
  },
  // 同步代表
  same (params) {
    return post('/member/same', params)
  },
  // 换届预览
  changePreview (params) {
    return postform('/member/changePreview?', params)
  },
  // 一键换届
  changeJc (params) {
    return postform('/member/changeJc?', params)
  },
  // 删除往届
  historymemberDel (params) {
    return post('/historymember/dels?', params)
  },
  // 导出代表名单
  exportnames (params) {
    exportFile('/member/exportnames?', params)
  },
  // 导出代表名册
  exportnamelist (params) {
    exportFile('/member/exportnamelist?', params)
  },
  // 导出代表照片”/“导出委员照片
  downloadHeadImg (params) {
    exportFile('/member/downloadHeadImg?', params)
  },
  // 导出代表照片”/“导出委员照片
  importPreview (params) {
    return postform('/member/importPreview', params)
  },
  // 导出代表照片”/“导出委员照片
  batchImport (params) {
    return filedownload('/member/batchImport', params)
  },
  // 导出代表照片”/“导出委员照片
  existsinfo (params) {
    return postform('/useraudit/existsinfo', params)
  },
  // 导出代表照片”/“导出委员照片
  lookUserLocation (params) {
    return post('/member/lookUserLocation?', params)
  },
  // 代表公开
  openMember (params) {
    return post('/wholeuser/batch/openMember?', params)
  },
  // 代表不公开
  closeMember (params) {
    return post('/wholeuser/batch/closeMember?', params)
  },
  // 取消换届预览
  memberChangeJCVersions (params) {
    return post('/member/changeJC/versions?', params)
  },
  // 取消换届
  memberChangeJC (params) {
    return post('/member/cancelchangeJC?', params)
  },
  // 换届
  memberChangeJc (params) {
    return post('/member/changeJc', params)
  }

}
export default memberInformation
