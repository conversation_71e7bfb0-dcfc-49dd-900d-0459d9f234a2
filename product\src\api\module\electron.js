// 导入封装的方法
import {
  get,
  post,
  postform,
  fileRequest
  // exportFile
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
const electron = {
  electronservicetype (params) {
    return post('/electronservicetype/list', params)
  },
  electronservicedels (params) {
    return post('/electronservicetype/dels', params)
  },
  electronservicetypeAddEdit (url, params) {
    return post(url, params)
  },
  sreenService: {
    info (params) {
      return post(`/appconfig/list?${params}`)
    },
    add (params) {
      return post('/appconfig/add', params)
    },
    edit (params) {
      return post('/appconfig/edit', params)
    }
  },
  general: {
    saveMeetFile (params, headers) {
      return post('/electronfile/saveMeetFile', params)
    },
    users (params) {
      return post('/pointree/users', params)
    },
    getSelectionChoose (params) {
      return post('/common/getSelectionChoose', params)
    },
    getUserList (params) {
      return post('/electronfilecontrol/listNode', params)
    },
    toFileControl (params) {
      return post('/electronfilecontrol/toFileControl', params)
    }
  },
  meetMng: {
    list (params, headers) {
      return post('/electronmeet/list', params)
    },
    attendList (params) {
      return post(`/meetinvite/findInvite${params}`)
    },
    add (params) {
      return post('/electronmeet/add', params)
    },
    edit (params) {
      return post('/electronmeet/edit', params)
    },
    info (params) {
      return post('/electronmeet/pcInfo/' + params)
    },
    findInvite (params) {
      return post('/electroninvite/findInvite' + params)
    },
    saveInvite (params) {
      return post('/electroninvite/saveInvite', params)
    },
    delInvite (params) {
      return post('/electroninvite/delInvite', params)
    },
    fileList (params) {
      return post('/electronfile/list', params)
    },
    delsFile (params) {
      return post('/electronfile/dels', params)
    },
    dels (params) {
      return post('/electronmeet/dels', params)
    }
  },
  fileMng: {
    updateSort (params) {
      return post('/electronfile/updateSort', params)
    },
    download (params, text) {
      return fileRequest('/electronfile/download', params, text)
    },
    edit (params) {
      return post('/electronfile/edit', params)
    },
    auth (params) {
      return post('/electronfilecontrol/fileControl', params)
    },
    dels (params) {
      return post('/electronfilecontrol/delFileControl', params)
    },
    authList (params) {
      return post('/electronfilecontrol/fileControlUserList', params)
    }
  },
  meetService: {
    list (params, headers) {
      return post('/electroncallservice/list', params)
    },
    edit (params, headers) {
      return post('/electroncallservice/edit', params)
    },
    dels (params, headers) {
      return post('/electroncallservice/dels', params)
    },
    updateStatus (params) {
      return post('/electroncallservice/updateStatusByIds', params)
    },
    officeOptions (params) {
      return get('/electronmeet/officeOptions', params)
    }
  },
  meetingType: {
    list (params, headers) {
      return post('/electrontype/list', params)
    },
    add (params) {
      return post('/electrontype/add', params)
    },
    edit (params) {
      return post('/electrontype/edit', params)
    },
    dels (params) {
      return post('/electrontype/dels', params)
    }
  },
  informMsg: {
    list (params) {
      return post('/electronmessage/list', params)
    },
    dels (params) {
      return post('/electronmessage/dels', params)
    },
    add (params) {
      return post('/electronmessage/add', params)
    },
    edit (params) {
      return post('/electronmessage/edit', params)
    },
    saveMessageUser (params) {
      return post('/electronmessageuser/saveMessageUser', params)
    },
    push (params) {
      return post('/electronmessage/updatePush?id=' + params)
    }
  },
  dataBase: {
    list (params) {
      return post('/electroninformationfile/list', params)
    },
    dels (params) {
      return post('/electroninformationfile/dels', params)
    },
    changeSort (params, headers) {
      return post('/electroninformationfile/changeSort', params, headers)
    },
    upload (params) {
      return post('/electroninformationfile/uploadInformationFile', params)
    }
  },
  equipment: {
    list (params) {
      return post('/electrondriver/list', params)
    },
    dels (params) {
      return post('/electrondriver/driver/del', params)
    },
    find (params) {
      return post('/electrondriver/ringDown', params)
    },
    add (params, headers) {
      return post('/electrondriver/init', params, headers)
    }
  }
}
export default electron
