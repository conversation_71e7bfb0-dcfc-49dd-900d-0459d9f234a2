// 导入封装的方法
import {
  post
} from '../http'

const keyWork = {
  linkagefocuswork: {
    list (params) {
      return post('/linkagefocuswork/list', params)
    },
    info (params) {
      return post('/linkagefocuswork/info/' + params)
    },
    add (params) {
      return post('/linkagefocuswork/add', params)
    },
    del (params) {
      return post('/linkagefocuswork/del/' + params)
    },
    dels (params) {
      return post('/linkagefocuswork/dels', params)
    },
    edit (params) {
      return post('/linkagefocuswork/edit', params)
    },
    count (params) {
      return post('/linkagefocuswork/count', params)
    },
    successfulTransformation (params) {
      return post('/linkagefocuswork/successfulTransformation', params)
    }
  },
  linkageworktracking: {
    list (params) {
      return post('/linkageworktracking/list', params)
    },
    info (params) {
      return post('/linkageworktracking/info/' + params)
    },
    add (params) {
      return post('/linkageworktracking/add', params)
    },
    del (params) {
      return post('/linkageworktracking/del/' + params)
    },
    dels (params) {
      return post('/linkageworktracking/dels', params)
    },
    edit (params) {
      return post('/linkageworktracking/edit', params)
    }
  }
}
export default keyWork
