// 导入封装的方法
import {
  post
} from '../http'
// 模块管理
const H5moduleManagement = {
  // newH5moduleList (params) {
  //   return post('/moduleapp/list?', params)
  // },
  newH5moduleList (params) {
    return post('/moduleapp/pc/list', params)
  },
  newH5moduleInfo (params) {
    return post(`/moduleapp/info/${params}`)
  },
  newH5moduleDel (params) {
    return post('/moduleapp/dels', params)
  },
  newH5moduleParentList (params) {
    return post('/moduleapp/parentList', params)
  },
  moduleappkeySelect (params) {
    return post('/moduleappkey/select/list', params)
  }
}
export default {
  ...H5moduleManagement
}
