// 导入封装的方法
import {
  post
} from '../http'
// 模块管理
const newH5modulAssembly = {
  // newH5moduleList (params) {
  //   return post('/moduleapp/list?', params)
  // },
  newH5moduleList (params) {
    return post('/moduleappkey/list', params)
  },
  newH5moduleInfo (params) {
    return post(`/moduleappkey/info/${params}`)
  },
  newH5moduleDel (params) {
    return post(`/moduleappkey/del/${params}`)
  },
  newH5moduleParentList (params) {
    return post('/moduleapp/parentList', params)
  }
}
export default {
  ...newH5modulAssembly
}
