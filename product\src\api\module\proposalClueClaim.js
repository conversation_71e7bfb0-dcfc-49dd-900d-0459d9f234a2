// 导入封装的方法
import {
  post,
  get
} from '../http'

const proposalClueClaim = {
  proposalSelfAdd(params) { // 新增本级线索
    return post('/linkage/clue/proposal/selfAdd', params)
  },
  proposalNormalEdit(params) { // 编辑级线索
    return post('/linkage/clue/proposal/normalEdit', params)
  },
  proposalSelfAllList(params) { // 本级所有线索列表
    return get('/linkage/clue/proposal/selfAllList', params)
  },
  proposalInfo (id, params) { // 提案线索详情
    return post(`/linkage/clue/proposal/info/${id}`, params)
  },
  proposalDel (id, params) { // 删除提案线索
    return post(`/linkage/clue/proposal/del/${id}`, params)
  },
  proposalMyClueList (params) { // 我的线索列表
    return get('/linkage/clue/proposal/myClueList', params)
  },
  proposalPendingList (params) { // 本级待审查线索列表
    return get('/linkage/clue/proposal/pendingList', params)
  },
  proposalSelfReportList (params) { // 本级已上报线索列表
    return get('/linkage/clue/proposal/selfReportList', params)
  },
  proposalStayList (params) { // 本级留存线索列表
    return get('/linkage/clue/proposal/stayList', params)
  },
  proposalRejectList (params) { // 本级已废弃（未采用）提案列表
    return get('/linkage/clue/proposal/rejectList', params)
  },
  proposalReportList (params) { // 已上报线索列表（上级数据）
    return get('/linkage/clue/proposal/reportList', params)
  },
  proposalAudit (params) { // 审查线索
    return post('/linkage/clue/proposal/audit', params)
  },
  proposalRejectToAudit (params) { // 不采用批量转为待审查
    return post('/linkage/clue/proposal/rejectToAudit', params)
  },
  proposalAdopt (params) { // 采用线索/发起提案
    return post('/linkage/clue/proposal/adopt', params)
  }
}
export default proposalClueClaim
