// 导入封装的方法
import {
  get,
  post,
  postform,
  exportFile,
  fileRequest,
  filedownload
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
export const exe = params => exportFile('', params)

const sinceManagement = {
  // 获取我的信息
  dutyreportGetMyInfo (params) {
    return post('/dutyreport/getMyInfo?', params)
  },
  // 获取履职报告详情
  dutyreportInfo (params) {
    return post('/dutyreport/info/' + params)
  },
  // 删除履职报告
  dutyreportDels (params) {
    return post('/dutyreport/dels?', params)
  },
  // 审核履职报告
  updateAuditStatus (params) {
    return post('/dutyreport/updateAuditStatus', params)
  },
  dutyusersituationgetDutyNum (params) { // 履职情况列表
    return post('/dutyusersituation/getDutyNum?', params)
  },
  dutyusersituationlist (params) { // 履职情况详情
    return post('/dutyusersituation/list?', params)
  },
  userdutyyearlist (params) { // 代表履职补录列表
    return post('/userdutyyear/list?', params)
  },
  addDutyUserSituations (url, params) { // 批量确认确认履职情况
    return post(`/dutyusersituation/${url}?`, params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  editStateByIds (params) { // 接收
    return post('/userdutyyear/editStateByIds?', params)
  },
  userdutyyeardels (params) { // 删除
    return post('/userdutyyear/dels?', params)
  },
  getIsAddDutyYear (params) { // 检查是否新增
    return post('/dutyusersituation/getIsAddDutyYear?', params)
  },
  dictionarypubkvs (params) { // 年份字典
    return post('/dictionary/pubkvs?', params)
  },
  dutyoptionslist (params) { // 获取履职项列表
    return post('/dutyoptions/list?', params)
  },
  dutyoptionsinfo (params) { // 获取履职项详情
    return post(`/dutyoptions/info/${params}`)
  },
  dutyoptionsurl (url, params) { // 新增 修改
    return post(`/dutyoptions/${url}`, params)
  },
  dutyoptionsdels (params) { // 删除
    return post('/dutyoptions/dels', params)
  },
  dutyitemlist (params) { // 获取履职项配置列表
    return post('/dutyitem/list', params)
  },
  dutyiteminfo (params) { // 获取履职项配置详情
    return post(`/dutyitem/info/${params}`)
  },
  dutyitemurl (url, params) { // 新增 修改
    return post(`/dutyitem/${url}`, params)
  },
  dutyitemdels (params) { // 删除
    return post('/dutyitem/dels', params)
  },
  outDutyDetailWord (params, name) { // 导出word（网页）履职补录
    fileRequest('/dutyusersituation/outDutyDetailWord', params, name)
  },
  dutyconfiggenerateDuty (params) { // 履职列表
    return post('/dutyConfigNum/getDutyConfigNum', params)
  },
  // 导出word
  getDutyInfoConfigListNum (params) { // 履职列表
    return post('/dutyConfigNum/getDutyInfoConfigListNum', params)
  },
  // 导出附件
  downloadZip (params) {
    // console.log(baseURL)
    // window.location.href = `${baseURL}/dutyreport/downloadZip?ids=${ids}&areaId=${areaId}&isRoutineMember=${isRoutineMember}`
    return filedownload('/dutyreport/downloadZip', params)
  },
  // dutyconfiggenerateDuty (params) { // 履职列表
  //   return post('/dutyconfig/generateDuty', params)
  // },
  // dutyconfiggenerateDuty (params) { // 履职列表
  //   return post('/dtyNum/list', params)
  // },
  getDutyNumLog (params) { // 履职档案日志列表
    return post('/dtyNum/getDutyNumLog', params)
  },
  getListHeader (params) { // 履职列表
    return post('/dutyconfig/getListHeader', params)
  },
  getDutyConfigInfo (params) { // 履职项配置列表
    return post('/dutyconfig/getDutyConfigInfo', params)
  },
  dutyconfigedit (params) { // 履职项配置列表
    return post('/dutyconfig/edit', params)
  },
  dutyyearEdit (params) { // 履职项配置列表
    return post('/dutyyear/edit', params)
  },
  // generateDutyDetail (params) { // 履履职详情
  //   return post('/dutyconfig/generateDutyDetail', params)
  // },
  generateDutyDetail (params) { // 履履职详情
    return post('/dutyConfigNum/getDutyInfoConfigNum?', params)
  },
  getDetailHeader (params) { // 获取履职详情表头
    return post('/dutyconfig/getDetailHeader', params)
  },
  getgenerateDuty (params) { // 生成履职档案
    return post('/duty/generateDuty', params)
  },
  getgenerateCommitteeDuty (params) { // 生成常委会组成人员履职档案
    return post('/duty/generateCommitteeDuty', params)
  },
  fillgenerallist (params) { // 履职填报列表
    return post('/fillgeneral/list', params)
  },

  saveFillGeneral (params) { // 履职填报列表
    return post('/fillgeneral/saveFillGeneral', params)
  },

  fillgeneraldels (params) { // 履职填报列表
    return post('/fillgeneral/dels', params)
  },
  fillgeneralinfo (params) { // 履职填报列表
    return post(`/fillgeneral/info/${params}`)
  },

  // 树接口
  treelist (params) {
    return post('/tree/list?', params)
  },
  dutyGenerateDuty (params) { // 履职情况统计
    return post('/duty/generateDuty?', params)
  },
  dutyGetDutyDetail (params) { // 履职情况统计
    return post('/duty/getDutyDetail?', params)
  },

  findActivityAuditFills (params) { // 履职档案审核列表
    return post('/activityfill/findActivityAuditFills?', params)
  },
  auditActivityFill (params) { // 履职档案审核列表
    return post('activityfill/auditActivityFill', params)
  },
  memberreport: {
    memberSubmitCase () {
      return get('/memberreport/memberSubmitCase')
    },
    list (params) {
      return post('/memberreport/list', params)
    },
    info (params) {
      return post(`/memberreport/info/${params}`)
    },
    add (params) {
      return post('/memberreport/add', params)
    },
    edit (params) {
      return post('/memberreport/edit', params)
    },
    dels (params) {
      return post('/memberreport/dels', params)
    }
  },
  // 履职信息录入
  memberInformationInput: {
    getDistInfo (params) {
      return post('/dutyApi/getDistInfo', params)
    },
    // 会议管理
    additionalrecordingmeetingList (params) {
      return post('/additionalrecordingmeeting/list', params)
    },
    additionalrecordingmeetingInfo (params) {
      return post(`/additionalrecordingmeeting/info/${params}`)
    },
    additionalrecordingmeetingAddEdit (url, params) {
      return post(url, params)
    },
    additionalrecordingmeetingDel (params) {
      return post(`/additionalrecordingmeeting/del/${params}`)
    },
    getMeetIngType (params) {
      return post('/additionalrecordingmeeting/getMeetIngType', params)
    },
    additionalrecordingAuthData (url, params) {
      return post(url, params)
    },
    // 活动管理
    additionalrecordingactivityList (params) {
      return post('/additionalrecordingactivity/list', params)
    },
    additionalrecordingactivityInfo (params) {
      return post(`/additionalrecordingactivity/info/${params}`)
    },
    additionalrecordingactivityAddEdit (url, params) {
      return post(url, params)
    },
    additionalrecordingactivityDel (params) {
      return post(`/additionalrecordingactivity/del/${params}`)
    },
    // 会议发言
    additionalrecordingspeakList (params) {
      return post('/additionalrecordingspeak/list', params)
    },
    additionalrecordingspeakInfo (params) {
      return post(`/additionalrecordingspeak/info/${params}`)
    },
    additionalrecordingspeakAddEdit (url, params) {
      return post(url, params)
    },
    additionalrecordingspeakDel (params) {
      return post(`/additionalrecordingspeak/del/${params}`)
    },
    // 撰写文章
    additionalrecordingarticleList (params) {
      return post('/additionalrecordingarticle/list', params)
    },
    additionalrecordingarticleInfo (params) {
      return post(`/additionalrecordingarticle/info/${params}`)
    },
    additionalrecordingarticleAddEdit (url, params) {
      return post(url, params)
    },
    additionalrecordingarticleDel (params) {
      return post(`/additionalrecordingarticle/del/${params}`)
    },
    // 基层履职建设
    additionalrecordingsubstrateList (params) {
      return post('/additionalrecordingsubstrate/list', params)
    },
    additionalrecordingsubstrateInfo (params) {
      return post(`/additionalrecordingsubstrate/info/${params}`)
    },
    additionalrecordingsubstrateAddEdit (url, params) {
      return post(url, params)
    },
    additionalrecordingsubstrateDel (params) {
      return post(`/additionalrecordingsubstrate/del/${params}`)
    },
    // 公益活动
    additionalrecordingwelfareList (params) {
      return post('/additionalrecordingwelfare/list', params)
    },
    additionalrecordingwelfareInfo (params) {
      return post(`/additionalrecordingwelfare/info/${params}`)
    },
    additionalrecordingwelfareAddEdit (url, params) {
      return post(url, params)
    },
    additionalrecordingwelfareDel (params) {
      return post(`/additionalrecordingwelfare/del/${params}`)
    },
    additionalrecordingwelfareUpdate (params) {
      return post('/additionalrecordingwelfare/updateVerification', params)
    },
    // 表彰奖励
    additionalrecordingmedalList (params) {
      return post('/additionalrecordingmedal/list', params)
    },
    additionalrecordingmedalInfo (params) {
      return post(`/additionalrecordingmedal/info/${params}`)
    },
    additionalrecordingmedalAddEdit (url, params) {
      return post(url, params)
    },
    additionalrecordingmedalDel (params) {
      return post(`/additionalrecordingmedal/del/${params}`)
    },
    additionalrecordingmedalUpdate (params) {
      return post('/additionalrecordingmedal/updateVerification', params)
    },
    // 其他事项
    additionalrecordingotherlList (params) {
      return post('/additionalrecordingother/list', params)
    },
    additionalrecordingotherInfo (params) {
      return post(`/additionalrecordingother/info/${params}`)
    },
    additionalrecordingotherAddEdit (url, params) {
      return post(url, params)
    },
    additionalrecordingotherlDel (params) {
      return post(`/additionalrecordingother/del/${params}`)
    }
  }
}

export default sinceManagement
