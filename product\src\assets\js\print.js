const Print = function (dom, options) {
  if (!(this instanceof Print)) return new Print(dom, options)
  this.options = this.extend({ noPrint: '.no-print' }, options)
  if ((typeof dom) === 'string') {
    this.dom = document.querySelector(dom)
  } else {
    this.isDOM(dom)
    this.dom = this.isDOM(dom) ? dom : dom.$el
  }
  this.init()
}
Print.prototype = {
  /**
  * 初始化
  */
  init: function () {
    var content = this.getStyle() + this.getHtml()
    console.log(content)
    this.writeIframe(content)
  },
  /**
  * 配置属性扩展
  */
  extend: function (obj, obj2) {
    for (var k in obj2) {
      obj[k] = obj2[k]
    }
    return obj
  },
  /**
  * 复制原网页所有的样式
  */
  getStyle: function () {
    var str = ''
    var styles = document.querySelectorAll('style,link')
    for (var i = 0; i < styles.length; i++) {
      // 打印属性全部放置在print.css文件中，不然在国产机上容易卡死
      if (styles[i].id === 'printCssId') {
        str += styles[i].outerHTML
      }
      // this.dom.classList.forEach(item => {
      //   if (styles[i].outerHTML.indexOf(item) !== -1) {
      //     str += styles[i].outerHTML
      //   }
      // })
    }
    str += '<style>' + (this.options.noPrint ? this.options.noPrint : '.no-print') + '{display:none;}</style>'
    str += '<style>*{margin: 0;padding: 0;}html,body,div{height: auto!important;box-sizing: border-box;}</style>'
    return str
  },
  // 表单赋值
  getHtml: function () {
    var inputs = document.querySelectorAll('input')
    var textareas = document.querySelectorAll('textarea')
    var selects = document.querySelectorAll('select')
    for (var k = 0; k < inputs.length; k++) {
      if (inputs[k].type == 'checkbox' || inputs[k].type == 'radio') { // eslint-disable-line
        if (inputs[k].checked == true) { // eslint-disable-line
          inputs[k].setAttribute('checked', 'checked')
        } else {
          inputs[k].removeAttribute('checked')
        }
      } else if (inputs[k].type == 'text') { // eslint-disable-line
        inputs[k].setAttribute('value', inputs[k].value)
      } else {
        inputs[k].setAttribute('value', inputs[k].value)
      }
    }
    for (var k2 = 0; k2 < textareas.length; k2++) {
      if (textareas[k2].type == 'textarea') { // eslint-disable-line
        textareas[k2].innerHTML = textareas[k2].value
      }
    }
    for (var k3 = 0; k3 < selects.length; k3++) {
      if (selects[k3].type == 'select-one') { // eslint-disable-line
        var child = selects[k3].children
        for (var i in child) {
          if (child[i].tagName == 'OPTION') { // eslint-disable-line
            if (child[i].selected == true) { // eslint-disable-line
              child[i].setAttribute('selected', 'selected')
            } else {
              child[i].removeAttribute('selected')
            }
          }
        }
      }
    }
    return this.dom.outerHTML
  },
  /**
  * 创建iframe
  */
  writeIframe: function (content) {
    var w, doc, iframe = document.createElement('iframe') // eslint-disable-line
    var f = document.body.appendChild(iframe)
    iframe.id = 'myIframe'
    iframe.setAttribute('style', 'position:absolute;width:0;height:0;top:-10px;left:-10px;')
    w = f.contentWindow || f.contentDocument
    doc = f.contentDocument || f.contentWindow.document
    doc.open()
    doc.write(content)
    setTimeout(() => {
      doc.close()
      this.toPrint(w)
      setTimeout(() => {
        // document.body.removeChild(iframe)
      }, 100)
    }, 1000)
  },
  /**
  打印
  */
  toPrint: function (frameWindow) {
    try {
      setTimeout(function () {
        frameWindow.focus()
        try {
          if (!frameWindow.document.execCommand('print', false, null)) {
            frameWindow.print()
          }
        } catch (e) {
          frameWindow.print()
        }
        frameWindow.close()
      }, 10)
    } catch (err) {
      console.log('err', err)
    }
  },
  isDOM: (typeof HTMLElement === 'object') ? // eslint-disable-line
    (obj) => {
      return obj instanceof HTMLElement
    } : (obj) => {
      return obj && typeof obj === 'object' && obj.nodeType === 1 && typeof obj.nodeName === 'string'
    }
}
const MyPlugin = {}
MyPlugin.install = function (Vue, options) {
  Vue.prototype.$print = Print
}
export default MyPlugin
