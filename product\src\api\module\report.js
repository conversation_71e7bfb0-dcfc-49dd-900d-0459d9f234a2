// 导入封装的方法
import {
  post
} from '../http'

const report = {
  add (params) {
    return post('/linkage/report/add', params)
  },
  edit (params) {
    return post('/linkage/report/edit', params)
  },
  selfList (params) {
    return post('/linkage/report/selfList', params)
  },
  allAuditList (params) {
    return post('/linkage/report/allAuditList', params)
  },
  noAuditList (params) {
    return post('/linkage/report/noAuditList', params)
  },
  noSignList (params) {
    return post('/linkage/report/noSignList', params)
  },
  signUpList (params) {
    return post('/linkage/report/signUpList', params)
  },
  myList (params) {
    return post('/linkage/report/myList', params)
  },
  audit (params) {
    return post('/linkage/report/audit', params)
  },
  info (params) {
    return post('/linkage/report/info/' + params)
  },
  dels (params) {
    return post('/linkage/report/dels', params)
  }
}
export default report
