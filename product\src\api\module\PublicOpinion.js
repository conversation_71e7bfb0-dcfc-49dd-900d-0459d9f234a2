// 导入封装的方法
import { post, filedownload } from '../http'

const PublicOpinion = {
  generalAdd (url, params) {
    return post(url, params)
  },
  socialinfoList (params) { // 社情民意列表
    return post('/socialinfo/list', params)
  },
  socialinfodels (params) { // 社情民意列表删除
    return post('socialinfo/dels', params)
  },
  socialinfolook (params) { // 社情民意详情【仅显示】
    return post('/socialinfo/look/' + params)
  },
  opinionoriginalInfo (params) { // 原稿详情
    return post(`/opinionoriginal/info/${params}`)
  },
  processimg (params) { // 查看办理进度图
    return filedownload('/socialinfo/processimg', params, 'arraybuffer')
  },
  socialinfoinfo (params) { // 社情民意详情【仅显示】
    return post('/socialinfo/info/' + params)
  },
  complatetask (params) { // 办理当前流程
    return post('/socialinfo/complatetask', params)
  },
  opinionexamineList (params) { // 查询采用批示列表
    return post('/opinionexamine/list', params)
  },
  opinionexamineDel (params) { // 采用批示列表删除
    return post('/opinionexamine/dels', params)
  },
  opinionexamineInfo (params) { // 采用批示列表删除
    return post('/opinionexamine/info/' + params)
  },
  dictionarylist (params) { // 字典
    return post('/dictionary/list', params)
  },
  opinionnumberInfo (params) { // 编号详情
    return post(`/opinionnumber/info/${params}`)
  },
  socialinfoCount (params) { // 来稿统计
    return post('/socialinfo/count', params)
  },
  socialinfoUpList (params) { // 来稿统计
    return post('/socialinfo/upList', params)
  },
  getPreArea (params) { // 来稿统计
    return post('/area/getPreArea?', params)
  },
  findRelationByType (params) { // 来稿统计
    return post('/userelation/findRelationByType', params)
  }
}
export default PublicOpinion
