// 导入封装的方法
import {
  post,
  get
} from '../http'
import Vue from 'vue'
const LargeScreen = {
  // 引导页大屏-济宁市政协
  getShiZhengXie (params) {
    return post('/zyDataScreen/userSituation', params)
  },
  // 引导页大屏-履职活动视图
  getBusinessData (params) {
    return post('/zyDataScreen/businessData', params)
  },
  // 引导页大屏-各区县政协
  getFoundation (params) {
    return post('/zyDataScreen/foundation', params)
  },
  // 委员履职-委员概括
  getMemberInfoList (params) {
    return post('/dutyConfigNumWeb/getMemberInfoList', params)
  },
  // 委员履职-提案概括
  getProposalInfoList (params) {
    return post('/dutyConfigNumWeb/getProposalInfoList', params)
  },
  // 委员履职-提案概括-图表数据
  getActivityInfoList (params) {
    return post('/proposal/proposalCount', params)
  },
  // 委员履职-反馈社情民意
  getSocialOpinionInfoList () {
    return post('/dutyConfigNumWeb/getSocialOpinionInfoList')
  },
  // 社情民意-类别统计
  categoryCount (params) {
    return post('/socialVisualize/categoryCount', params)
  },
  // 社情民意-最新社情民意信息
  todayList (params) {
    return post('/socialVisualize/todayList', params)
  },
  // 社情民意-近五年来稿量
  recentYearsCount (params) {
    return post('/socialVisualize/recentYearsCount', params)
  },
  // 社情民意-近七日来稿量
  recentlyCount (params) {
    return post('/socialVisualize/recentlyCount', params)
  },
  // 社情民意-采用情况统计
  journalCount (params) {
    return post('/socialVisualize/journalCount', params)
  },
  // 委员履职-会议履职
  getMeetingInfo () {
    return post('/dutyConfigNumWeb/getMeetingInfo')
  },
  // 委员履职-会议履职
  meetingDuty (params) {
    return post('/cockpit/meetingDuty', params)
  },
  // 委员履职-政协活动
  getCPPCCActivity (params) {
    return post('/dutyConfigNumWeb/getCPPCCActivity', params)
  },
  // 委员履职-委员履职排行榜
  getDutyConfigNumWeb (params) {
    return post('/dutyConfigNum/getDutyConfigNumWeb', params)
  },
  // 提案在线大屏-提案分类
  countBySubmitType (params) {
    return post('/proposal/public/countBySubmitType', params)
  },
  // 提案在线大屏-个人提案和集体提案前十名
  personalOrCollectiveRank (params) {
    return post('/proposal/public/personalOrCollectiveRank', params)
  },
  // 提案在线大屏-提案办理情况统计
  transactRank (params) {
    return post('/proposal/public/transactRank', params)
  },
  // 委员统计大屏-委员履职形式数量
  getDeputyActivity (params) {
    return post('/dutyConfigNumWeb/getDeputyActivity', params)
  },
  // 提案在线大屏-提案总体情况今年数据
  allSituationCount (params) {
    return post('/proposal/public/allSituationCount', params)
  },
  // 提案在线大屏-提案类别分析
  getProposalCategory (params) {
    return post('/proposalScreen/getProposalCategory', params)
  },
  // 提案在线大屏-提案总体情况历年数据
  allHistorySituationCount (params) {
    return post('/proposal/public/allHistorySituationCount', params)
  },
  // 提案在线大屏-提案办理
  mainAndAssistCount (params) {
    return post('/proposal/public/mainAndAssistCount', params)
  },
  // 获取地区id
  getAreaId (params) {
    return post('/zyDataScreen/getAreaId', params)
  },
  // 委员信息统计大屏 - 男女比例
  memberCount (params) {
    return post('/member/count', params)
  },
  // 委员信息统计大屏 - 所属专委会
  getSpecialCommittee (params) {
    return post('/member/getSpecialCommittee', params)
  },
  // 机关保障大屏 - 周活动安排
  weeklyactivityAppList (params) {
    return post('/weeklyactivity/appList', params)
  },
  // 提案在线大屏 - 热点分析
  aggsByTags (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/common/aggsByTags`, params)
  },
  // 机关保障大屏 - 重点工作整体进展情况 - 年度工作
  getYearWorkPlansWeb (params) {
    return post('/yearworkplan/getYearWorkPlansWeb', params)
  },
  // 机关保障大屏 - 重点工作整体进展情况 - 月度
  getMonthlyWorkPlansWeb (params) {
    return post('/monthlyworkplan/getMonthlyWorkPlansWeb', params)
  },
  // 机关保障大屏 - 重点工作整体进展情况 - 阶段性工作
  getPhaseWorksWeb (params) {
    return post('/phasework/getPhaseWorksWeb', params)
  },
  // 机关保障大屏 - 已完成重点工作 - 年度工作
  yearworkplanList (params) {
    return post('/yearworkplan/pcList', params)
  },
  // 机关保障大屏 - 已完成重点工作 - 月度工作
  monthlyworkplanList (params) {
    return post('/monthlyworkplan/list', params)
  },
  // 机关保障大屏 - 已完成重点工作 - 阶段性工作
  phaseworkList (params) {
    return post('/phasework/list', params)
  },
  // 宣传信息大屏 - 图片轮播
  zyinfodetailList (params) {
    return post('/zyinfodetail/list', params)
  },
  // 机关保障大屏 - 重点工作责任委室统计
  getResponsibleCommitteeOffice (params) {
    return post('/yearworkplan/getResponsibleCommitteeOffice', params)
  },
  // 学习交流大屏 - 热门书籍统计
  librarycountBook (params) {
    return post('/librarycount/book', params)
  },
  // 学习交流大屏 - 委员阅读统计
  librarycountUser (params) {
    return post('/librarycount/user', params)
  },
  // 学习交流大屏 - 读书会
  findActivitys (params) {
    return post('/zyDataScreen/bookClub', params)
  },
  // 学习交流大屏 - 书籍数量和访问量
  usageBook (params) {
    return post('/cockpit/usageBook', params)
  },
  // 学习交流大屏 - 各单位访问量
  domainActivityGroup (params) {
    return post('/cockpit/domainActivityGroup', params)
  },
  // 数字政协大屏 - 平台用户数
  joinapp (params) {
    return post('/wholeuser/joinapp', params)
  },
  // 数字政协大屏 - 访问人次和访问量
  pcAllLogUsers (params) {
    return post('/pcAllLogUsers', params)
  },
  // 数字政协大屏 - 提案在线
  getProposalOnline (params) {
    return post('/proposalScreen/getProposalOnline', params)
  },
  // 数字政协大屏 - 年度协商计划
  getTopicsStateNumJiNing (params) {
    return post('/zyDataScreen/consultDataScreen/getTopicsStateNumJiNing', params)
  },
  // 数字政协大屏 - 委员结构
  getMemberNum (params) {
    return post('/zyDataScreen/getMemberNum', params)
  },
  // 机关保障大屏 - 月度重点工作的计划和实施数据
  getMonthlyWorkPlansDateWeb (params) {
    return post('/monthlyworkplan/getMonthlyWorkPlansDateWeb', params)
  },
  // 数字政协大屏 - 报送单位社情民意
  getEachRegionSocialOpinions (params) {
    return post('/zyDataScreen/getEachRegionSocialOpinions', params)
  },
  // 宣传信息大屏 - 图片轮播
  getPublicityMaterial (params) {
    return post('/zyDataScreen/getPublicityMaterial', params)
  },
  // 提案在线大屏-提案类别分析
  proposalCount (params) {
    return post('/proposal/proposalCount', params)
  },
  // 机关保障大屏-收发文统计
  getBpmData (params) {
    return post('/zyDataScreen/consultDataScreen/getOASystem', params)
    // return get(`http://112.6.110.185:99/api/getBpmData.php?CODE=${params}`)
  },
  // 社情民意大屏-各区县得分情况
  cityCount (params) {
    return post('/socialVisualize/cityCount', params)
  },
  // 社情民意大屏-民主党派及工商联得分
  deleAmount (params) {
    return post('/socialVisualize/deleAmount', params)
  },
  // 社情民意大屏-民主党派及工商联来稿量
  deleScore (params) {
    return post('/socialVisualize/deleScore', params)
  },
  // 社情民意大屏-信息来稿量
  amount (params) {
    return post('/socialVisualize/amount', params)
  },
  // 委员履职大屏-提案概括
  zyProposalCount (params) {
    return post('/zyDataScreen/proposalCount', params)
  },
  // 数字政协大屏-平台用户数
  getUserCenterCount (params) {
    return post('/zyDataScreen/getUserCenterCount', params)
  },
  // 数字政协大屏-政协协商饼图
  negotiationAnalysisAPI (params) {
    return post('/consultApi/negotiationAnalysisAPI', params)
  }
}
export default LargeScreen
